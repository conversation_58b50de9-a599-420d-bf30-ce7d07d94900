import argparse
import re
import sys
from subprocess import check_output, CalledProcessError


INFO = "\033[96m"
SUCCESS = "\033[92m"
WARNING = "\033[93m"
ERROR = "\033[91m"
END = "\033[0m"


def update_commit_message(filename, ticket_number):
    with open(filename, "r+") as commit_file:
        contents = commit_file.readlines()
        commit_msg = contents[0]

        if ticket_number in commit_msg:
            print(f"{INFO}Commit message already contains branch ticket number.{END}")
            return 0

        new_commit_msg = f"{ticket_number}: {commit_msg}"

        contents[0] = new_commit_msg
        commit_file.seek(0)
        commit_file.writelines(contents)
        commit_file.truncate()

    print(f"{SUCCESS}Ticket number {ticket_number} added to commit message.{END}")
    return 0


def get_branch_name():
    try:
        return check_output(["git", "symbolic-ref", "--short", "HEAD"]).decode("UTF-8").strip()
    except CalledProcessError:
        return ""


def main(argv=None):
    parser = argparse.ArgumentParser()
    parser.add_argument("filenames", nargs="+")
    parser.add_argument("--branch-to-skip", nargs="+", default=["main", "develop", "release/[0-9]+.[0-9]+.[0-9]+"])
    parser.add_argument("--regex", default="BLUEB-[0-9]+")

    args = parser.parse_args(argv)
    branch_name = get_branch_name()

    if branch_name and any([re.fullmatch(skip, branch_name) for skip in args.branch_to_skip]):
        print(f"{INFO}Skipping branch {branch_name}.{END}")
        return 0

    ticket_number = re.findall(args.regex, branch_name)

    if not ticket_number:
        print(f"{ERROR}All feature and bugfix branch names must include a Jira ticket number.{END}")
        print(f"{WARNING}Did you miss-typed or forget to include one?{END}")
        print(f'{WARNING}If this is intentional, `SKIP=add-ticket-number git commit -m "yolo"` to skip this hook.{END}')
        return 1

    return update_commit_message(args.filenames[0], ticket_number[0])


if __name__ == "__main__":
    sys.exit(main())
