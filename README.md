# Cypress Integration Testing

This module contains all user interface integration tests. These tests validate communication between the web apps, API, and backend services. A developer can write integration tests during the development process and validate their feature works end to end before it is merged onto `develop`.

## Setup

cd tests
yarn install
yarn prepare

## Testing Against Local Environment

 - Follow the https://github.com/comcast-bluebird/databee-local-dev README to start your docker images
 - Start the Yarn services for `interface` and `cluster-interface` from this repo: https://github.com/comcast-bluebird/databee-ui
 - Create a copy of the template.env file and populate it with the required information
 - Start the Cypress GUI: `yarn run cypress:local`
    - OR Start the Cypress CLI: ` yarn cypress run --browser chrome --env ENVIRONMENT=local_creds`

Other Cypress flags available:
```
--spec <spec path starting cypress/*>
--headed <show browser launch and test run>
```

## Testing Against AWS Environment

Below is the command to execute with the default profile name and authenticate the Microsoft login code.

```bash
aadawscli --profile-name default
```

Select the AWS account "databee-us-dev."

If you don't have a "databee-us-dev" AWS account, can you please contact <PERSON>, and he will provide access to that account?

## Starting Cypress to open the Cypress UI or Cypress CLI and run all the specs

Note: If you want to see a report by Allure, you must first run the following command to clean the report folders from previous runs or skip if this is your first run.

```bash
yarn clean:report
```

You can either run the entire cypress test suite in the terminal by running:
Go to this path, '/iris-application/cypress_tests', and run the below command. Note: this will not generate the allure report.

```bash
yarn cypress:run
```

To generate and view Allure report you must first run Cypress tests using the above command, then run the following:

```bash
yarn gen:report
```

Once the report is generated, you will notice the two directories (allure-report & allure-results), use the following command to open the report:

```bash
yarn open:report
```

You may also use the following command to run (clean:report, cypress:run, gen:report) in sequence:

```bash
yarn cypress:test
```

Or you can open up the cypress UI to show you the test execution as it happens by running:
Go to this path, '/iris-application/cypress_tests', and run the below command.

```bash
yarn cypress:open
```

## Starting Cypress using Tags

### Scope based tags :

-   @smoke
-   @regression
-   @full
-   @e2e_setup
-   @e2e_tests
-   @e2e_cleanup
-   data_faker

### Component based tags :

    - dashboard
    - data
    - search
    - config_sso
    - config_user
    - notification
    - my_profile
    - datacollector
    - data_lake
    - config
    - compliance
    - entitlements
    - exposure

you can check out the link https://etwiki.sys.comcast.net/display/BP/Tag+based+testing+approach added more info on the listed tags.

You can either run the entire cypress test suite in the terminal by running headless:

```bash
yarn cypress run --headless --spec './cypress/e2e/ui/' --env grepTags=tags,grepFilterSpecs=true,allure=true
```

You can skip running the tests with specific tag using the invert option: prefix the tag with the character -.

If you do not run any tests with the tag "@smoke"

```bash
yarn cypress run --headless --spec './cypress/e2e/ui/' --env grepTags=-@smoke,grepFilterSpecs=true,allure=true
```

If you want to run all tests with tag @regression but without tag @smoke:

```bash
yarn cypress run --headless --spec './cypress/e2e/ui/' --env grepTags=@regression+-@smoke,grepFilterSpecs=true,allure=true
```

## Running the Cypress scripts based on the environment and AWS region to retrieve the application Credentials from AWS Secrets Management

If you want to run a local, dev, and staging environment without modifying the default AWS_REGION
In the code, the default environment is set to "local." You can use the command below to override the default value.

```bash
yarn cypress run --env allure=true,ENVIRONMENT=<env>  --spec './cypress/e2e/ui/'
```

If you want to run the default local environment with modifications to the AWS_REGION
In the code, the default AWS_REGION is set to "us-east-1." You can use the below command to override the default value.

```bash
yarn cypress run --env allure=true,AWS_REGION=<region>  --spec './cypress/e2e/ui/'
```

If you want to run in a staging environment with a different AWS_REGION to get the AWS secret data

```bash
yarn cypress run --env allure=true,ENVIRONMENT=<env>,AWS_REGION=<region>  --spec './cypress/e2e/ui/'
```

## Running the Cypress scripts based on the Bucket Name and AWS region to create and delete the buckets in AWS S3 services.

If you want to run a local, dev, and staging environment without modifying the default S3_BUCKET_NAME, AWS_REGION
In the code, the default S3_BUCKET_NAME is set to "databee-cypress-test" You can use the command below to override the default value.

```bash
yarn cypress run --env allure=true,ENVIRONMENT=<env>  --spec './cypress/e2e/ui/'
```

If you want to run the default local environment with modifications to the S3_BUCKET_NAME, AWS_REGION
In the code, the default S3_BUCKET_NAME is set to "databee-cypress-test" and AWS_REGION is set to "us-east-1." You can use the below command to override the default value.

```bash
yarn cypress run --env allure=true,S3_BUCKET_NAME=<bucket>,AWS_REGION=<region>  --spec './cypress/e2e/ui/'
```

If you want to run in a staging environment with a different S3_BUCKET_NAME, AWS_REGION to create and delete the bucket in AWS S3

```bash
yarn cypress run --env allure=true,ENVIRONMENT=<env>,S3_BUCKET_NAME=<bucket>,AWS_REGION=<region>  --spec './cypress/e2e/ui/'
```

## Cross Browser Testing

### Testing using various browsers (Default: Electron)

For example:

-   chrome
-   firefox
-   electron

Simple add the following flag to the cypress run command and set the desired browser type:

```bash
--browser=chrome
```

# GitHub Workflows

For details on how Cypress tests are executed on feature branches using GitHub Actions, including vCluster setup, test execution, and cleanup, see the [Cypress Test For Feature Testing Guide](.github/workflows/cypress-feature-testing-guide.md).
