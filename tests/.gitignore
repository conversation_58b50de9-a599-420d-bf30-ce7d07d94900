# Cypress artifacts
cypress/screenshots/
cypress/videos/
cypress/mochawesome-report/

**/**/cypress-test-results.json

# Using zero-installs method for faster install (https://yarnpkg.com/getting-started/qa#which-files-should-be-gitignored)
# Remove pnp when we're ready to use zero-installs
.pnp.*
# !.yarn/cache (uncomment this when we're ready to use zero-installs)
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
node_modules
yarn-error.*

# Allure test results and reports
allure-report
allure-results
test-results.xml
cypress/downloads

#Sensative Files
cypress/fixtures/*.xml
cypress/fixtures/feeds
cypress/fixtures/dataFeedConfigurations
cypress/context

#Environment Variables
.env