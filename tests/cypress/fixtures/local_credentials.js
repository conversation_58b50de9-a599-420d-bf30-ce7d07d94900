const dotenv = require("dotenv");

dotenv.config();

const localCredentials = {
    CLUSTER_ADMIN_PASSWORD: process.env.DATABEE_USER_PASS,
    CLUSTER_API: process.env.CLUSTER_API,
    CLUSTER_SUPPORT_PASSWORD: process.env.DATABEE_USER_PASS,
    CLUSTER_UI: process.env.CLUSTER_UI,
    CLUSTER_USER_TEST_IDS: {
        ADMIN_USER_TEST: process.env.CLUSTER_ADMIN_USER_TEST,
        SUPPORT_USER_TEST: process.env.CLUSTER_SUPPORT_USER_TEST,
    },
    CLUSTER_USER_IDS: {
        ADMIN: process.env.CLUSTER_ADMIN,
        SUPPORT: process.env.CLUSTER_SUPPORT,
    },
    DASHBOARD_RESET: {
        URL: process.env.DASHBOARD_RESET_URL,
        USERS: {
            ADMIN: process.env.DASHBOARD_RESET_ADMIN,
            ANALYST: process.env.DASHBOARD_RESET_ANALYST,
            DATA_ENGINEER: process.env.DASHBOARD_RESET_DATA_ENGINEER,
        },
        PASS: {
            ADMIN: process.env.DATABEE_USER_PASS,
            ANALYST: process.env.DATABEE_USER_PASS,
            DATA_ENGINEER: process.env.DATABEE_USER_PASS,
        },
    },
    DATABEE_ADMIN_PASSWORD: process.env.DATABEE_USER_PASS,
    DATABEE_ANALYST_PASSWORD: process.env.DATABEE_USER_PASS,
    DATABEE_DATAENGINEER_PASSWORD: process.env.DATABEE_USER_PASS,
    DATABEE_UI_NAME: process.env.DATABEE_UI_NAME,
    DATABEE_UI: process.env.DATABEE_UI,
    DATABEE_UI_ID: process.env.DATABEE_UI_ID,
    DATABEE_USER_TEST_IDS: {
        ADMIN_USER_TEST: process.env.DATABEE_ADMIN_USER_TEST,
        ANALYST_USER_TEST: process.env.DATABEE_ANALYST_USER_TEST,
    },
    DATABEE_USER_IDS: {
        ADMIN: process.env.DATABEE_ADMIN,
        ANALYST: process.env.DATABEE_ANALYST,
        DATAENGINEER: process.env.DATABEE_DATAENGINEER,
    },
    DATABEE_USER_ROLES: {
        Admin: "ADMINISTRATOR",
        Analyst: "SECURITY_ANALYST",
        DataEngineer: "DATA_ENGINEER",
    },
    CYPRESS_DISABLE: {
        URL: process.env.CYPRESS_DISABLE_URL,
        ADMIN_USERNAME: process.env.CYPRESS_DISABLE_ADMIN_USERNAME,
        ADMIN_PASS: process.env.DATABEE_USER_PASS,
        UI_NAME: process.env.CYPRESS_DISABLE_UI_NAME,
    },
    SNOWFLAKE_CREDENTIALS: {
        ACCOUNT_IDENTIFIER: process.env.SNOWFLAKE_ACCOUNT_IDENTIFIER,
        USERNAME: process.env.SNOWFLAKE_USERNAME,
        PASSWORD: process.env.SNOWFLAKE_PASSWORD,
        ROLE: process.env.SNOWFLAKE_ROLE,
        WAREHOUSE: process.env.SNOWFLAKE_WAREHOUSE,
        DATABASE: process.env.SNOWFLAKE_DATABASE,
        PRIVATE_KEY: process.env.SNOWFLAKE_PRIVATE_KEY,
    },
    API_INGEST_BASIC: {
        Username: process.env.API_INGEST_BASIC_USERNAME,
        Password: process.env.API_INGEST_BASIC_PASSWORD,
        BaseUrl: process.env.API_INGEST_BASIC_BASIC_URL,
    },
    DATABEE_COMPLIANCE_ROLE: {
        USERNAME: process.env.DATABEE_COMPLIANCE_ROLE_USERNAME,
        PASSWORD: process.env.DATABEE_USER_PASS,
    },
    CONTENT_DELIEVERY: {
        USERNAME: process.env.CONTENT_DELIEVERY_USERNAME,
        CLIENT_ID: process.env.CONTENT_DELIEVERY_CLIENT_ID,
        CLIENT_SECRET: process.env.CONTENT_DELIEVERY_CLIENT_SECRET,
        SECRET_VALUE: process.env.CONTENT_DELIEVERY_SECRET_VALUE,
    },
    SERVICENOW_CREDENTIALS: {
        AUTH_TYPE: process.env.SERVICENOW_AUTH_TYPE,
        USERNAME: process.env.SERVICENOW_USERNAME,
        PASSWORD: process.env.SERVICENOW_PASSWORD,
        CLIENT_KEY: process.env.SERVICENOW_CLIENT_KEY,
        CLIENT_SECRET: process.env.SERVICENOW_CLIENT_SECRET,
        TOKEN_URL: process.env.SERVICENOW_TOKEN_URL,
    },
    TENANT_API: process.env.TENANT_API,
    GITHUB_URL: process.env.GITHUB_URL,
};

module.exports = localCredentials;
