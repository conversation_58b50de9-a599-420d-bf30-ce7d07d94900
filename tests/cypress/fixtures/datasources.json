{"datacollector_datasource": {"label": "Fortinet Firewall", "configType": "Data Collector", "name": "fortinet_enable_disable_", "logSource": "Syslog", "port": 8071, "mode": "TCP", "format": "syslog-rfc5424", "tags": [{"key": "dns_type", "value": "syslog"}, {"key": "dns_type_2", "value": "syslog"}], "filters": [{"key": "Inclusion", "value": "logdesc=\"Authentication success\""}, {"key": "Exclusion", "value": "logdesc=\"Authentication failure\""}]}}