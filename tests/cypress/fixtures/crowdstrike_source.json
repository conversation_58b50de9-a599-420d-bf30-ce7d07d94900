{"meta": {"query_time": 0.061821093, "writes": {"resources_affected": 0}, "powered_by": "<PERSON><PERSON><PERSON>", "trace_id": "a9d45e8c-d8c3-453d-902a-e70d39503889"}, "resources": [{"agent_id": "b6497b4a26a84d", "aggregate_id": "aggind:b6497b4a26a84d:26628", "alleged_filetype": "exe", "associated_files": [], "cid": "92012896127c", "cloud_indicator": "false", "cmdline": "C:\\Users\\<USER>\\Roaming\\Python\\Python312\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe --browser chrome --language-binding python --output json", "composite_id": "92012896127c:ind:b6497b4a26a84d:************-*************", "confidence": 10, "context_timestamp": "2024-06-21T05:50:30.910Z", "control_graph_id": "ctg:b6497b4a26a84d:26628", "crawled_timestamp": "2024-06-21T05:55:33.221261722Z", "created_timestamp": "2024-06-21T05:51:33.232291702Z", "data_domains": ["Endpoint"], "description": "This file meets the machine learning-based on-sensor AV protection's lowest-confidence threshold for malicious files.", "device": {"agent_load_flags": "1", "agent_local_time": "2024-06-14T12:33:25.764Z", "agent_version": "7.16.18605.0", "bios_manufacturer": "LENOVO", "bios_version": "F8CN57WW(V2.20)", "cid": "92012896127c", "config_id_base": "65994763", "config_id_build": "18605", "config_id_platform": "3", "device_id": "b6497b4a26a84d", "external_ip": "*******", "first_seen": "2023-11-15T00:18:04Z", "groups": ["18704e21288243b58e4c76266d38caaf"], "hostinfo": {"active_directory_dn_display": ["Win Computers", "Win Computers"], "domain": "LOCAL"}, "hostname": "CLW1017-2060", "last_seen": "2024-06-21T05:49:39Z", "local_ip": "*************", "mac_address": "dr-22-45-5f-eo-8b", "machine_domain": "LOCAL", "major_version": "10", "minor_version": "0", "modified_timestamp": "2024-06-21T05:50:40Z", "os_version": "Windows 11", "ou": ["Win Computers"], "platform_id": "0", "platform_name": "Windows", "pod_labels": null, "product_type": "1", "product_type_desc": "Workstation", "site_name": "Default-First-Site-Name", "status": "normal", "system_manufacturer": "LENOVO", "system_product_name": "20VE"}, "display_name": "MLSensor-<PERSON><PERSON>", "email_sent": true, "falcon_host_link": "https://falcon.us-2.crowdstrike.com/activity-v2/detections/92012896127c:ind:b6497b4a26a84d:************-*************?_cid=g04000yyzfxqmvbncadppr6yduthjuxy", "filename": "selenium-manager.exe", "filepath": "\\Device\\HarddiskVolume3\\Users\\deep.patel2\\AppData\\Roaming\\Python\\Python312\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe", "global_prevalence": "common", "grandparent_details": {"cmdline": "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe -noexit -command \"try { . \\\"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\browser\\media\\shellIntegration.ps1\\\" } catch {}\"", "filename": "powershell.exe", "filepath": "\\Device\\HarddiskVolume3\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "local_process_id": "6192", "md5": "9d8e30daf21108092d5980c931876b7e", "process_graph_id": "pid:b6497b4a26a84d:************", "process_id": "************", "sha256": "3247bcfd60f6dd25f34cb74b5889ab10ef1b3ec72b4d4b3d95b5b25b534560b8", "timestamp": "2024-06-21T05:55:16Z", "user_graph_id": "uid:b6497b4a26a84d:S-1-5-21-1909377054-3469629671-4104191496-8873", "user_id": "S-1-5-21-1909377054-3469629671-4104191496-8873", "user_name": "deep.patel2"}, "has_script_or_module_ioc": "true", "id": "ind:b6497b4a26a84d:************-*************", "indicator_id": "ind:b6497b4a26a84d:************-*************", "ioc_context": [{"ioc_description": "\\Device\\HarddiskVolume3\\Users\\deep.patel2\\AppData\\Roaming\\Python\\Python312\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe", "ioc_source": "library_load", "ioc_type": "hash_sha256", "ioc_value": "6cc186fc7d11f3b70932f20cf701be791ad41a9b6a091452253b1774539e10d6", "md5": "f3f45ff4bfa86c89cc5869bf673a9f82", "sha256": "6cc186fc7d11f3b70932f20cf701be791ad41a9b6a091452253b1774539e10d6", "type": "module"}], "ioc_description": "\\Device\\HarddiskVolume3\\Users\\deep.patel2\\AppData\\Roaming\\Python\\Python312\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe", "ioc_source": "library_load", "ioc_type": "hash_sha256", "ioc_value": "6cc186fc7d11f3b70932f20cf701be791ad41a9b6a091452253b1774539e10d6", "ioc_values": [], "is_synthetic_quarantine_disposition": true, "local_prevalence": "unique", "local_process_id": "8680", "logon_domain": "testy", "md5": "f3f45ff4bfa86c89cc5869bf673a9f82", "name": "MLSensor-<PERSON><PERSON>", "objective": "Falcon Detection Method", "parent_details": {"cmdline": "\"C:\\Program Files\\Python312\\python.exe\" .\\intro.py", "filename": "python.exe", "filepath": "\\Device\\HarddiskVolume3\\Program Files\\Python312\\python.exe", "local_process_id": "20416", "md5": "499ec6ca890861b763a2458472c27f81", "process_graph_id": "pid:b6497b4a26a84d:************", "process_id": "************", "sha256": "cfd05f0ad320310c4891a5fd297ec96e25f4a23fe64f3fb06df67004198c4ab5", "timestamp": "2024-06-21T05:50:56Z", "user_graph_id": "uid:b6497b4a26a84d:S-1-5-21-1909377054-3469629671-4104191496-8873", "user_id": "S-1-5-21-1909377054-3469629671-4104191496-8873", "user_name": "deep.patel2"}, "parent_process_id": "************", "pattern_disposition": 2176, "pattern_disposition_description": "Prevention/Quarantine, process was blocked from execution and quarantine was attempted.", "pattern_disposition_details": {"blocking_unsupported_or_disabled": false, "bootup_safeguard_enabled": false, "critical_process_disabled": false, "detect": false, "fs_operation_blocked": false, "handle_operation_downgraded": false, "inddet_mask": false, "indicator": false, "kill_action_failed": false, "kill_parent": false, "kill_process": false, "kill_subprocess": false, "operation_blocked": false, "policy_disabled": false, "process_blocked": true, "quarantine_file": true, "quarantine_machine": false, "registry_operation_blocked": false, "rooting": false, "sensor_only": false, "suspend_parent": false, "suspend_process": false}, "pattern_id": 5714, "platform": "Windows", "poly_id": "AACSASiWEnxKlII2unYBuIawWAtJhpJND5EeX5LSvTXAJQAATiFfVUrBLB7Ulc6Ba5axvBJWrWQIYvFhy7YizFba2aCHoQ==", "process_end_time": "1718949030", "process_id": "************", "process_start_time": "1718949030", "product": "epp", "quarantined_files": [{"filename": "\\Device\\HarddiskVolume3\\Users\\deep.patel2\\AppData\\Roaming\\Python\\Python312\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe", "id": "b6497b4a26a84d_6cc186fc7d11f3b70932f20cf701be791ad41a9b6a091452253b1774539e10d6", "sha256": "6cc186fc7d11f3b70932f20cf701be791ad41a9b6a091452253b1774539e10d6", "state": "quarantined"}], "scenario": "NGAV", "seconds_to_resolved": 0, "seconds_to_triaged": 0, "severity": 10, "severity_name": "Informational", "sha1": "0000000000000000000000000000000000000000", "sha256": "6cc186fc7d11f3b70932f20cf701be791ad41a9b6a091452253b1774539e10d6", "show_in_ui": true, "source_products": ["Falcon Insight"], "source_vendors": ["CrowdStrike"], "status": "new", "tactic": "Machine Learning", "tactic_id": "CSTA0004", "technique": "Sensor-based ML", "technique_id": "CST0007", "timestamp": "2024-06-21T05:50:31.549Z", "tree_id": "26628", "tree_root": "************", "triggering_process_graph_id": "pid:b6497b4a26a84d:************", "type": "ldt", "updated_timestamp": "2024-06-21T05:55:33.221248576Z", "user_id": "S-1-5-21-1909377054-3469629671-4104191496-8873", "user_name": "deep.patel2", "user_principal": "<EMAIL>"}]}