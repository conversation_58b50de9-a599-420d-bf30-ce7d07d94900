{"feeds": {"fortinet_firewall": [{"label": "Fortinet Firewall", "configType": "Data Collector", "name": "fortinet_inclusion_filter", "logSource": "Syslog", "port": 8021, "mode": "TCP", "format": "syslog-rfc5424", "filters": [{"key": "Inclusion", "value": "logdesc=\"Authentication success\""}]}, {"label": "Fortinet Firewall", "configType": "Data Collector", "name": "fortinet_tcp_tls", "logSource": "TCP", "port": 8023, "format": "leef", "enableTLS": true}], "checkpoint_firewall": [{"label": "Checkpoint Firewall", "configType": "Data Collector", "name": "checkpoint_with_tags", "logSource": "Syslog", "port": 8031, "mode": "TCP", "format": "cef", "tags": [{"key": "dns_type", "value": "syslog"}]}, {"label": "Checkpoint Firewall", "configType": "Data Collector", "name": "checkpoint_tcp_tls", "logSource": "TCP", "port": 8041, "format": "leef", "enableTLS": true}], "amtrak_beyondtrust": [{"label": "BeyondTrust", "configType": "Data Collector", "name": "beyondtrust_tcp_tls", "logSource": "TCP", "port": 8043, "format": "leef", "enableTLS": true}], "amtrak_redseal": [{"label": "redseal", "configType": "Data Collector", "name": "redseal_with_tag", "logSource": "Syslog", "port": 8032, "mode": "TCP", "format": "cef", "tags": [{"key": "dns_type", "value": "syslog"}]}], "amtrak_symantec_endpoint": [{"label": "Symantec Endpoint", "configType": "Data Collector", "name": "symantec_endpoint_exclusion_filter", "logSource": "Syslog", "port": 8033, "mode": "TCP", "format": "syslog-rfc5424", "filters": [{"key": "Exclusion", "value": "Smart Rule"}]}], "postgresql_events": [{"label": "Postgresql Events", "configType": "Data Collector", "name": "postgresql_events", "logSource": "Flat File", "refreshInterval": 1, "format": "cef", "sourceFiles": {"linux": ["/var/mwfe2eautomation/*.log"], "windows": ["C:\\Program Files\\mwfe2eautomation\\*.log"]}, "exclusionFiles": {"linux": ["/var/mwfe2eautomation/disconnection2.log"], "windows": ["C:\\Program Files\\mwfe2eautomation\\disconnection2.log"]}}]}}