/// <reference types="cypress"/>

import {
    actionSearch,
    clearSearchedAction,
    createNewAction,
    navigateToRemediationActions,
    openCreateNewActionDialog,
} from "../iris/remediation_actions/remediation_action_utils";

describe(
    "Demo Tests | Remediation Actions - Comprehensive Validation",
    { tags: ["demo_tests", "remediation_actions"] },
    () => {
        beforeEach(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").DATABEE_UI);
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
        });

        afterEach(() => {
            cy.clickLogout();
        });

        const expectedHeaders = [
            "Name",
            "Connection",
            "Status",
            "Last Run Status",
            "Last Run Time",
            "Next Run Time",
            "Edited By",
            "Last Edit",
            "Actions",
        ];

        const expectedHistoryHeaders = [
            "Name",
            "Connection",
            "Start Time",
            "End Time",
            "Duration",
            "Total Records",
            "Ticket Number",
            "Run Info",
        ];

        const expectedButtons = ["History", "New", "Delete"];

        it("should validate Remediation Actions page & UI Components", () => {
            navigateToRemediationActions();
            cy.get("#search-query-text").should("be.visible").and("be.enabled");
            cy.get("#actions-table").should("be.visible");

            cy.get("#actions-table thead th").then(($headers) => {
                cy.wrap($headers).each(($header, index) => {
                    if (index > 0) {
                        cy.wrap($header.find("div")).should("contain.text", expectedHeaders[index - 1]);
                    }
                });

                cy.wrap($headers.first().find("input[type='checkbox']")).then(($headerCheckbox) => {
                    if ($headerCheckbox.length > 0) {
                        cy.findByRole("button", { name: "Delete" }).should("be.disabled");
                        cy.wrap($headerCheckbox).check();

                        cy.get("#actions-table tbody tr").each(($row) => {
                            cy.wrap($row.find("td").first().find('input[type="checkbox"]')).should("be.checked");
                        });
                        cy.findByRole("button", { name: "Delete" }).should("be.enabled");

                        cy.wrap($headerCheckbox).uncheck();

                        cy.get("#actions-table tbody tr").each(($row) => {
                            cy.wrap($row.find("td").first().find('input[type="checkbox"]')).should("not.be.checked");
                        });

                        cy.findByRole("button", { name: "Delete" }).should("be.disabled");
                    }
                });
            });

            expectedButtons.forEach((buttonName) => {
                cy.findByRole("button", { name: buttonName }).should("be.visible");
                if (buttonName === "Delete") {
                    cy.findByRole("button", { name: buttonName }).should("be.disabled");
                } else {
                    cy.findByRole("button", { name: buttonName }).should("be.enabled");
                }
            });

            cy.get("#actions-table tbody tr").then(($rows) => {
                if ($rows.length > 0) {
                    cy.wrap($rows.first().find("td").eq(1).find("button"))
                        .invoke("text")
                        .then((firstRowName) => {
                            const actionName = firstRowName.trim();
                            actionSearch(actionName);
                            cy.wrap($rows).should("have.length", 1);
                            cy.wrap($rows.first().find("td").eq(1).find("button")).should("contain.text", actionName);
                            clearSearchedAction();
                        });
                    cy.wrap($rows.first().find("td").first().find('input[type="checkbox"]'))
                        .check()
                        .should("be.checked");
                    cy.findByRole("button", { name: "Delete" }).should("be.enabled");
                    cy.wrap($rows.first().find("td").first().find('input[type="checkbox"]'))
                        .uncheck()
                        .should("be.not.checked");
                    cy.findByRole("button", { name: "Delete" }).should("be.disabled");

                    cy.wrap($rows.first()).then(($row) => {
                        cy.wrap($row.find("td").last().find("button").eq(0)).click();
                    });

                    cy.findByRole("menu").within(() => {
                        cy.get("button[role='menuitem']").should("have.length.greaterThan", 0);
                        cy.get("button[role='menuitem']").contains("Edit").should("be.visible");
                        cy.get("button[role='menuitem']").contains("History").should("be.visible");

                        // Check for Start or Pause based on status
                        cy.get("button[role='menuitem']").then(($buttons) => {
                            const buttonTexts = Array.from($buttons).map((btn) => btn.textContent);
                            const hasStart = buttonTexts.includes("Start");
                            const hasPause = buttonTexts.includes("Pause");

                            expect(hasStart || hasPause).to.equal(true);
                            cy.log(`Found menu options: ${buttonTexts.join(", ")}`);
                        });
                    });
                } else {
                    cy.log("No data rows found in the table.");
                }
            });
        });

        it("should validate Action History page", () => {
            navigateToRemediationActions();
            cy.get("#actions-table tbody tr").then(($rows) => {
                if ($rows.length > 0) {
                    cy.wrap($rows.first().find("td").eq(1).find("button"))
                        .invoke("text")
                        .then((firstRowName) => {
                            const actionName = firstRowName.trim();
                            cy.wrap($rows.first()).then(($row) => {
                                cy.wrap($row.find("td").last().find("button").eq(0)).click();
                            });
                            cy.findByRole("menu").within(() => {
                                cy.get("button[role='menuitem']").contains("History").click();
                            });
                            cy.findByRole("heading", { level: 2 })
                                .invoke("text")
                                .then((text) => {
                                    expect(text).to.equal("Action History");
                                });
                            cy.get("#actions-history-table").should("be.visible");
                            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Searching...");
                            cy.get("#actions-history-table tbody tr").then(($historyRows) => {
                                actionSearch(actionName);
                                cy.wrap($historyRows.first().find("td").eq(0)).should("contain.text", actionName);
                                clearSearchedAction();
                                cy.wrap($historyRows).each(($row) => {
                                    cy.wrap($row).find("td").eq(0).should("contain.text", actionName);
                                });
                            });
                        });
                } else {
                    cy.log("No data rows found in the table.");
                }

                cy.get("#actions-history-table thead th").each(($header, index) => {
                    cy.wrap($header).find("div").should("contain.text", expectedHistoryHeaders[index]);
                });

                cy.findByRole("button", { name: "Actions" }).should("be.enabled").click();
                cy.findByRole("heading", { level: 2 })
                    .invoke("text")
                    .then((text) => {
                        expect(text).to.equal("Actions");
                    });
                cy.checkForErrors("Remediation Actions", []);
            });
        });

        it("should validate create new remediation action flow", () => {
            navigateToRemediationActions();
            const actionName = `demo-test-${Date.now()}`;
            openCreateNewActionDialog();
            createNewAction(
                "ServiceNow",
                actionName,
                "Description Text",
                "Open Cybersecurity Schema Framework (OCSF)",
                "Account Change",
                { "User ID": { IsNotNone: true }, "User Result": { IsNotNone: true } },
                "Ticket Title",
                "Ticket Description",
                '{"table": "incident"}',
                "12:00 am",
                "All",
                false
            );
        });
    }
);
