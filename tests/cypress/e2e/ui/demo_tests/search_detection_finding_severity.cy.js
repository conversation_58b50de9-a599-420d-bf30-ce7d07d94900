/// <reference types="cypress"/>
import { clickSearch, setPageSize, setSearchParams, setSearchTable } from "../iris/search/search_utils";

describe("Harvey Dent | Search Detection Findings: Validate Severity Levels", { tags: ["demo_tests"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
    });

    it("Validate Severity Levels | Critical, High, Fatal: Not in Search", () => {
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
        setSearchTable("Search For:", "Detection Finding");
        const filterParams = {
            "Severity ID:": { "Not In:": ["Critical", "High", "Fatal"] },
        };
        setSearchParams(filterParams);
        clickSearch();
        cy.findByText("Loading Histogram...").should("not.exist");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
        setPageSize("100");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
        cy.get("#search-results thead th").then(($headers) => {
            const severityIndex = [...$headers].findIndex((th) => th.textContent === "Severity ID");
            expect(severityIndex).to.be.greaterThan(-1);
            cy.get("#search-results tbody tr").each(($row) => {
                const severity = $row.find("td").eq(severityIndex).find("span").text();
                expect(["Critical", "High", "Fatal"]).to.not.include(severity);
            });
        });
    });
});
