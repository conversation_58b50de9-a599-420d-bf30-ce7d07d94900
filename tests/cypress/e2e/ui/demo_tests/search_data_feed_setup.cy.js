/// <reference types="cypress"/>

import { deleteDataSource, gotoAddNewDataSourcePage, validateNewDataSourceFlow } from "../iris/data_feed/data_utils";
import { goToDataTab } from "../iris/navigation_rbac/navigation_utils";
import { clickSearch, setSearchTable } from "../iris/search/search_utils";

describe("Core Platform | Validate Search Functionality and Data Feed Setup Flow", { tags: ["demo_tests"] }, () => {
    let createdSources = [];
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
    });

    afterEach(() => {
        cy.clickLogout();
    });

    it("should validate search functionality with Authentication table", () => {
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
        setSearchTable("Search For:", "Authentication");
        clickSearch();
        cy.findByText("Loading Histogram...").should("not.exist");
        cy.findByText("Searching...").should("not.exist");
        cy.get("#search-entities canvas").should("have.length.greaterThan", 0);
        cy.get("table#search-results").should("exist");
    });

    it("should validate data feed setup flow", () => {
        ["API Ingest", "Azure Blob", "HTTP Collector", "AWS S3"].forEach((feedType) => {
            gotoAddNewDataSourcePage();
            const { sourceName } = validateNewDataSourceFlow("CrowdStrike", feedType);
            createdSources.push(sourceName);
        });
        ["Flat File", "Syslog", "TCP"].forEach((collectorType) => {
            gotoAddNewDataSourcePage();
            const { sourceName } = validateNewDataSourceFlow("CrowdStrike", "Data Collector", collectorType);
            createdSources.push(sourceName);
        });
    });

    after(() => {
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        goToDataTab();
        if (createdSources.length > 0) {
            createdSources.forEach((src) => deleteDataSource(src));
            createdSources = [];
        }
        cy.clickLogout();
    });
});
