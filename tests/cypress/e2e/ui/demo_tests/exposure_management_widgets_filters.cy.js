/// <reference types="cypress"/>

import { checkTableSeverity } from "../cyber_ui/exposure/exposure_utils";
import { validateAllWidgetsDisplayWithContent } from "../iris/dashboards/dashboard_utils";

describe("Exposure Management | Validate widgets and clear filter", { tags: ["demo_tests"] }, () => {
    const timeRange = "Past 1 Week";

    const exposureNonTableWidgets = [
        "Threat Overview",
        "Active Devices Observed",
        "Devices without Owners",
        "Applications without Owners",
        "Vulnerability Findings By Severity",
    ];

    const exposureTableWidgets = [
        "Threat Overview",
        "Potential Device Owners",
        "Devices with Most Applications",
        "Most Common Vulnerabilities",
        "Applications with Most Vulnerabilities",
        "Potential Application Owners",
        "Applications with Most Vulnerabilities",
        "Owners of Devices and Applications with Most Vulnerabilities",
    ];

    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Exposure", "Exposure Management");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading data...");
    });

    it("Validate widgets on Exposure Management Page", () => {
        validateAllWidgetsDisplayWithContent();
        cy.get(".date-range-picker.dropdown").within(() => {
            cy.findByRole("textbox").type(timeRange).type("{enter}");
        });
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading data...");
        cy.get("body").then(($body) => {
            if ($body.find("[role='alert']").length > 0) {
                cy.get('[role="alert"]').should("not.have.class", "alert-danger");
            }
        });
        const assertWidgetTitlesValidation = (titles, selector) => {
            titles.forEach((title) => {
                cy.get(selector).contains(`${title} (${timeRange})`).should("be.visible");
            });
        };
        assertWidgetTitlesValidation(exposureNonTableWidgets, "div.card:has(.card-body .row) h5");
        assertWidgetTitlesValidation(exposureTableWidgets, "div.card:has(.card-body table) h5");
    });

    it("Validate Threat Overview Widget Filtering", () => {
        cy.get("div.card:has(.card-body .row) h5")
            .contains(`${exposureNonTableWidgets[0]}`)
            .parentsUntil("div.card")
            .parent()
            .find(".card-body")
            .within(() => {
                cy.get("g.highcharts-legend text").each(($el) => {
                    cy.wrap($el)
                        .invoke("text")
                        .then((text) => {
                            if (["Fatal", "Critical"].includes(text)) {
                                cy.wrap($el).click();
                            }
                        });
                });
                checkTableSeverity({
                    allowedSeverities: ["Fatal", "Critical"],
                    shouldInclude: false,
                });
                cy.findByRole("button", { name: "Clear Filter" }).should("be.visible").click();
                checkTableSeverity({
                    allowedSeverities: ["Fatal", "Critical", "High", "Medium", "Low"],
                    shouldInclude: true,
                });
            });
    });
});
