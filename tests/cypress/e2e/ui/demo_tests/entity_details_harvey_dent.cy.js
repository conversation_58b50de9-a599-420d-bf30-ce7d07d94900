/// <reference types="cypress"/>

import {
    applyNodeGraphSelectFilter,
    eventTimelineSingleFilterValidation,
    findEventValidateEventDataInScrollableEventList,
    openNodeGraphFilters,
    toggleEventsCheckboxesAndValidate,
    toggleOwnedDevicesCheckboxAndValidate,
    validateEventTextOnly,
    validateFirstColumnOfUserDetails,
} from "../cyber_ui/entity_view/entity_utils";
import {
    pivotTableWidgetAndVerifyNavigation,
    validateAllWidgetsDisplayWithContent,
} from "../iris/dashboards/dashboard_utils";

describe("Harvey Dent | Expected Content Displayed on Entity Details Page", { tags: ["demo_tests"] }, () => {
    const userDetails = [
        "Organization",
        "Type",
        "Account",
        "Name",
        "Full Name",
        "Email Address",
        "Job Title",
        "Manager",
    ];

    const nonNumericUserDetails = ["Type", "Account", "Full Name", "Job Title", "Manager"];

    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
    });

    it("Validate | User Details Information", () => {
        cy.navigateTopNav("Console", "Overview");
        validateAllWidgetsDisplayWithContent();
        pivotTableWidgetAndVerifyNavigation("Most Findings By User (Past 1 Week)", "Harvey Dent");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading... This might take several minutes");
        cy.findByRole("heading", { level: 1 }).should("contain.text", "Harvey Dent").should("be.visible");
        cy.scrollTo("top", { easing: "swing" });
        validateFirstColumnOfUserDetails(userDetails, nonNumericUserDetails);
    });

    it("Validate | Event Timeline, Detection Findings, Authentication", () => {
        cy.visit(`${Cypress.env("credentials").DATABEE_UI}entities/user/8`);
        cy.findByRole("heading", { level: 1 }).should("contain.text", "Harvey Dent").should("be.visible");
        cy.scrollTo("bottom");
        findEventValidateEventDataInScrollableEventList("Triggered 'MITRE ATT&CK Techniques Detection Chain'", 5);
        findEventValidateEventDataInScrollableEventList("ET MALWARE Win32/Bumblebee Loader Checkin Activity", 0, 2);
        findEventValidateEventDataInScrollableEventList("Triggered 'Proactive Phishing Detection Chain'", 3);
        eventTimelineSingleFilterValidation("Email Activity", "email_activity");
        validateEventTextOnly("<NAME_EMAIL>");
        toggleEventsCheckboxesAndValidate("check");
        toggleEventsCheckboxesAndValidate("uncheck");
        toggleOwnedDevicesCheckboxAndValidate("check");
        toggleOwnedDevicesCheckboxAndValidate("uncheck");
        eventTimelineSingleFilterValidation("Authentication", "authentication");
        validateEventTextOnly("NLTM success to desktop-132");
    });

    it("Validate | Node Graph Filters", () => {
        cy.visit(`${Cypress.env("credentials").DATABEE_UI}entities/user/8`);
        cy.findByRole("heading", { level: 1 }).should("contain.text", "Harvey Dent").should("be.visible");
        openNodeGraphFilters();
        applyNodeGraphSelectFilter("authentication-activity", "Logon");
        applyNodeGraphSelectFilter("authentication-status", "Success");
        cy.get(".offcanvas-body").scrollTo("bottom");
        cy.findByRole("button", { name: "Apply" }).should("be.visible").click();
        cy.get("div[role='dialog']").should("not.be.visible");
    });
});
