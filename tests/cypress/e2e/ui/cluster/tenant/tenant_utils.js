// Methods

export function searchTenantByName(tenantName) {
    cy.findByText("Name").next("input").type(tenantName);
}

export function searchTenantById(tenantId) {
    cy.findByText("Tenant ID").next("input").type(tenantId);
}

export function navigateTenantDetails(tenantName, tenantId = null) {
    if (tenantId !== null) {
        searchTenantById(tenantId);
    }
    searchTenantByName(tenantName);

    cy.setInput("Active", "uncheck");

    cy.get(".rt-tbody .rt-tr")
        .contains(tenantName)
        .parent()
        .within(() => {
            if (tenantId !== null) {
                cy.contains(tenantId).should("be.visible");
            }
            cy.findByRole("link", { name: "Details" }).should("be.visible").click();
        });

    cy.findByText("Tenant Details").should("be.visible");
}

export function disableTenant() {
    cy.findByRole("button", { name: "Disable" }).should("be.enabled").click();
    cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
    cy.findByRole("alert").should("be.visible");
    cy.contains("Tenant is disabled").should("be.visible");
    cy.findByRole("button", { name: "Enable" }).should("be.visible");
    cy.findByRole("button", { name: "Submit" }).should("be.disabled");
}

export function selectSecurityEntitlements(select = true) {
    if (select) {
        cy.findByLabelText("Security Hygiene").then(($checkbox) => {
            if (!$checkbox.is(":checked")) {
                cy.findByLabelText("Security Hygiene").check();
            }
        });

        cy.findByLabelText("Security Threats").then(($checkbox) => {
            if (!$checkbox.is(":checked")) {
                cy.findByLabelText("Security Threats").check();
            }
        });
    } else if (!select) {
        cy.findByLabelText("Security Hygiene").then(($checkbox) => {
            if ($checkbox.is(":checked")) {
                cy.findByLabelText("Security Hygiene").uncheck();
            }
        });

        cy.findByLabelText("Security Threats").then(($checkbox) => {
            if ($checkbox.is(":checked")) {
                cy.findByLabelText("Security Threats").uncheck();
            }
        });
    }
    cy.findByRole("button", { name: "Submit" }).click();
    cy.contains("Tenant details updated.").should("be.visible");
}

export function enableTenant() {
    cy.findByRole("button", { name: "Enable" }).should("be.enabled").click();
    cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
    cy.findByRole("alert").should("be.visible");
    cy.contains("Tenant is enabled").should("be.visible");
    cy.findByRole("button", { name: "Disable" }).should("be.visible");
    cy.findByRole("button", { name: "Submit" }).should("be.enabled");
}
