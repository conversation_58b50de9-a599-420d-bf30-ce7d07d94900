/// <reference types="cypress"/>
import {
    accessNotification,
    canNotNavigateTenantCreate,
    navigateTenantCreate,
    navigateToHelpLinks,
    navigateToMyProfile,
} from "./navigation_utils";

describe("Cluster: RBAC page access checks", { tags: ["smoke", "full", "cluster_test"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").CLUSTER_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    it("Admin: User can access profile page", () => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").CLUSTER_UI);
        cy.manualLogin(
            Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
            Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
        );
        navigateToMyProfile();
    });

    it("Admin can access Cluster Help links", () => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").CLUSTER_UI);
        cy.manualLogin(
            Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
            Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
        );
        navigateToHelpLinks();
    });

    it("Admin: User can access notification page", () => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").CLUSTER_UI);
        cy.manualLogin(
            Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
            Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
        );
        accessNotification();
    });

    it("Support user can not access Create Tenant page", () => {
        cy.manualLogin(
            Cypress.env("credentials").CLUSTER_USER_IDS.SUPPORT,
            Cypress.env("credentials").CLUSTER_SUPPORT_PASSWORD
        );
        canNotNavigateTenantCreate();
    });

    it("Admin user can access Create Tenant page", () => {
        cy.manualLogin(
            Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
            Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
        );
        navigateTenantCreate();
    });
});
