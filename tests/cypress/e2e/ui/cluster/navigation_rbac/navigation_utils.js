// Methods
export function navigateTenantCreate() {
    cy.findByText("Create Tenant").click();
    cy.findByText("Tenant Details").should("be.visible");
}

export function canNotNavigateTenantCreate() {
    if (cy.findByText("Create Tenant").should("not.exist")) {
        cy.log("User does not have access to create Tenant");
    }
}

export function navigateToHelpLinks() {
    cy.navigateTopRightNav("Documentation", "API Documentation");
    cy.findByText("DataBee Cluster API").should("be.visible");
    cy.findAllByRole("button").eq(0).click({ force: true });
    cy.findByText("Online Cluster Administration Manual").should("be.visible");
}

export function accessSSOpage() {
    cy.navigateTopRightNav("Configuration", "Access Management");
    cy.findByRole("link", { name: "Single Sign-On (SSO)" }).should("be.visible");
    cy.findByText("There are no SSO Connections configured").should("be.visible");
    cy.findByRole("button", { name: "Add Connection" }).should("be.visible");
}

export function accessNotification() {
    cy.navigateTopRightNav("Notifications");
    cy.findByRole("link", { name: "See All" }).click();
    cy.findByText("All").should("be.visible");
    cy.findByText("Unread").should("be.visible");
    cy.get(".ml-5 input.form-control").should("be.visible").type("ABC").and("have.value", "ABC");
}

export function navigateToMyProfile() {
    cy.navigateTopRightNav("My Profile");
    cy.findByText("My Profile").click();
    cy.findByRole("button", { name: "Change Password" }).should("be.visible");
    cy.get("body").then(($body) => {
        if ($body.find('button:contains("Generate API Key")').length > 0) {
            // If Generate API Key button exists, click it
            cy.findByRole("button", { name: "Generate API Key" }).click();
        } else {
            // If Generate API Key doesn't exist, click Show API Key instead
            cy.findByRole("button", { name: "Show API Key" }).click();
        }
    });
    // Check for Hide API Key button (which appears after showing)
    cy.findByRole("button", { name: "Hide API Key" }).should("be.visible");

    // Check for Revoke API Key button
    cy.findByRole("button", { name: "Revoke API Key" }).should("be.visible");

    // Find the Copy to clipboard button using a more resilient approach
    cy.findAllByText("Copy to clipboard").should("be.visible");
    cy.findByText("Theme Mode").should("be.visible");
}
