/// <reference types="cypress"/>

import { verifyManageUserAccountsUI } from "../../iris/account/account_utils";
import { accessUsers, configNotExist } from "../../iris/navigation_rbac/navigation_utils";

describe(
    "Cluster: Configuration -> Access Management (Misc)",
    { tags: ["config_user", "full", "cluster_test"] },
    () => {
        beforeEach(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").CLUSTER_UI);
        });

        afterEach(() => {
            cy.clickLogout();
        });

        it(
            "should allow Cluster Admin user to access and verify Manage User Accounts and User details page UI objects",
            { tags: "regression" },
            () => {
                cy.manualLogin(
                    Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
                    Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
                );
                accessUsers();
                verifyManageUserAccountsUI();
            }
        );

        it("should prevent Cluster Support user from creating a DataBee Cluster user", { tags: "regression" }, () => {
            cy.manualLogin(
                Cypress.env("credentials").CLUSTER_USER_IDS.SUPPORT,
                Cypress.env("credentials").CLUSTER_SUPPORT_PASSWORD
            );
            configNotExist();
        });
    }
);
