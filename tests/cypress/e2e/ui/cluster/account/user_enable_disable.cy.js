/// <reference types="cypress"/>

import { disableUser, enableUser, logoutWithinTest } from "../../iris/account/account_utils";
import { accessUsers } from "../../iris/navigation_rbac/navigation_utils";

describe(
    "Cluster: Configuration -> Access Management (Enable/Disable)",
    { tags: ["config_user", "full", "cluster_test"] },
    () => {
        before(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").CLUSTER_UI);
        });

        it(
            "should allow Cluster Admin user to deactivate user and ensure user not able to login in DataBee portal",
            { tags: "regression" },
            () => {
                cy.manualLogin(
                    Cypress.env("credentials").CLUSTER_USER_TEST_IDS.ADMIN_USER_TEST,
                    Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
                );
                accessUsers();
                const userEmail = Cypress.env("credentials").CLUSTER_USER_TEST_IDS.SUPPORT_USER_TEST;
                cy.findByText("Username").next("input").click().type(userEmail);
                cy.get("body").as("body");
                cy.findByRole("cell", { name: userEmail })
                    .parent("tr")
                    .within(($tr) => {
                        if ($tr.text().includes("Inactive")) {
                            cy.wrap($tr).findByRole("button", { name: "Enable" }).should("be.enabled").click();
                            cy.get("@body")
                                .findByRole("dialog")
                                .within(() => {
                                    cy.findByText("Yes").click();
                                });
                        }
                    });
                cy.findByText("Username").next("input").clear();
                disableUser(Cypress.env("credentials").CLUSTER_USER_TEST_IDS.SUPPORT_USER_TEST);
                logoutWithinTest();
                cy.manualLogin(
                    Cypress.env("credentials").CLUSTER_USER_TEST_IDS.SUPPORT_USER_TEST,
                    Cypress.env("credentials").CLUSTER_SUPPORT_PASSWORD
                );
                cy.findByRole("button", { name: "Login" }).should("be.visible");
                cy.findByText("Invalid username or password").should("be.visible").and("exist");
                cy.manualLogin(
                    Cypress.env("credentials").CLUSTER_USER_TEST_IDS.ADMIN_USER_TEST,
                    Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
                );
                accessUsers();
                enableUser(Cypress.env("credentials").CLUSTER_USER_TEST_IDS.SUPPORT_USER_TEST);
                logoutWithinTest();
                cy.manualLogin(
                    Cypress.env("credentials").CLUSTER_USER_TEST_IDS.SUPPORT_USER_TEST,
                    Cypress.env("credentials").CLUSTER_SUPPORT_PASSWORD
                );
                logoutWithinTest();
            }
        );
    }
);
