/// <reference types="cypress"/>

import {
    cancelDashboardRequest,
    goToContentDelivery,
    purchaseDashboard,
    removePurchasedDashboard,
    requestDashboard,
    USER_ROLES,
} from "../../shared/configuration/content_delivery_utils";
import { navigateTenantDetails } from "../tenant/tenant_utils";

describe("Cluster: Content Delivery", { tags: ["full", "content_delivery"] }, () => {
    before(() => {
        cy.visit(Cypress.env("credentials").CLUSTER_UI);
        cy.manualLogin(
            Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
            Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
        );

        const tenantName = Cypress.env("credentials").DATABEE_UI.split("//")[1].split(".")[0];
        navigateTenantDetails(tenantName);

        // Check if the Application Security dashboard is already purchased and remove if so
        removePurchasedDashboard("Application Security");

        // Check if the Application Security dashboard has been requested and cancel if so
        cancelDashboardRequest("Application Security");

        cy.clickLogout();
    });

    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(USER_ROLES.admin.username, USER_ROLES.admin.password);

        goToContentDelivery();

        requestDashboard("Application Security", true);

        cy.findByTitle("My Profile")
            .parent()
            .within(($el) => {
                cy.wrap($el).click();
                cy.findByText("Logout").click();
            });
    });

    afterEach(() => {
        cy.clickLogout();
    });

    it("Allows the support team to cancel a dashboard request", () => {
        cy.visit(Cypress.env("credentials").CLUSTER_UI);
        cy.manualLogin(
            Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
            Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
        );
        const tenantName = Cypress.env("credentials").DATABEE_UI.split("//")[1].split(".")[0];
        navigateTenantDetails(tenantName);

        // Confirm that the request is visible
        cy.findByText("Dashboard purchase requests").should("be.visible");
        cy.findByText("Pending requests").should("be.visible");

        cancelDashboardRequest("Application Security", true);

        cy.findByRole("button", { name: "Submit" }).click();
        cy.contains("Tenant details updated.").should("be.visible");
    });

    it("Allows the support team to approve a dashboard request", () => {
        cy.visit(Cypress.env("credentials").CLUSTER_UI);
        cy.manualLogin(
            Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
            Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
        );
        const tenantName = Cypress.env("credentials").DATABEE_UI.split("//")[1].split(".")[0];
        navigateTenantDetails(tenantName);

        // Confirm that the request is visible
        cy.findByText("Dashboard purchase requests").should("be.visible");
        cy.findByText("Pending requests").should("be.visible");

        cy.contains(".list-group-item", "Application Security").within(() => {
            cy.findByRole("button", { name: "Cancel" }).should("be.visible");
        });

        // Approve the request
        purchaseDashboard("Application Security", true);

        // Close the list of purchased dashboards by clicking outside
        cy.findByText("Content Management").click();

        // Submit the form
        cy.findByRole("button", { name: "Submit" }).click();
        cy.contains("Tenant details updated.").should("be.visible");
    });
});
