/// <reference types="cypress"/>
import createNotificationAPI from "./notification_utils";

describe("Cluster: Notifications", { tags: ["full", "notification", "cluster_test"] }, () => {
    Object.entries(Cypress.env("credentials").CLUSTER_USER_IDS).forEach(([UserName, UserId]) => {
        it(`${UserName} can send notification`, { tags: "regression" }, () => {
            cy.clearCookies();
            const testrandomString = Math.random().toString(36).substring(8);
            const newTitle = `Title${testrandomString}`;
            const newMessage = `Message${testrandomString}`;
            const userRole = `${UserName}` === "SUPPORT" ? "SUPPORT" : "CLUSTER_ADMIN";
            cy.visit(Cypress.env("credentials").CLUSTER_UI);
            cy.manualLogin(UserId, Cypress.env("credentials")[`CLUSTER_${UserName}_PASSWORD`]);
            cy.findByTitle("My Profile").click();
            cy.findByText("My Profile").click();
            cy.get("body").then(($body) => {
                if ($body.find('button:contains("Generate API Key")').length > 0) {
                    // If Generate API Key button exists, click it
                    cy.findByRole("button", { name: "Generate API Key" }).click();
                } else {
                    // If Generate API Key doesn't exist, click Show API Key instead
                    cy.findByRole("button", { name: "Show API Key" }).click();
                }
            });
            // Check for Hide API Key button (which appears after showing)
            cy.findByRole("button", { name: "Hide API Key" }).should("be.visible");

            // Check for Revoke API Key button
            cy.findByRole("button", { name: "Revoke API Key" }).should("be.visible");

            // Find the Copy to clipboard button using a more resilient approach
            cy.findAllByText("Copy to clipboard").should("be.visible");
            cy.findByLabelText("API Key")
                .invoke("val")
                .then((APIKey) => {
                    createNotificationAPI(newTitle, newMessage, userRole, APIKey);
                });
            cy.findByTitle("Notifications").click();
            cy.findByRole("link", { name: "See All" }).click();
            cy.findByText(newTitle).should("be.visible");
            cy.findByText(newMessage).should("be.visible");
            cy.findAllByRole("button", { name: "Delete" }).first().click();
            cy.findByRole("alert").find("div").last().should("have.text", "Notification deleted successfully");
            cy.clickLogout();
        });
    });
});
