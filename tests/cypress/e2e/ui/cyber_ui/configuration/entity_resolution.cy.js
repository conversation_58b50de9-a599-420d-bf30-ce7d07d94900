describe("Tenant: Configuration -> System -> Entity Resolution", { tags: ["config", "full"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    const invalidDeviceEntityConfig = {
        "Virtual Device Age Out Time *": "DataBee-Virtual",
        "Physical Device Age Out Time *": "DataBee-Physical",
    };

    const validDeviceEntityConfig = {
        "Virtual Device Age Out Time *": 6,
        "Physical Device Age Out Time *": 200,
        "Internal CIDR Blocks": "10.0.0./8",
        "Internal Hostnames": "databee-buzz",
    };

    // Open either device or user dialog and update form with data
    function updateEntityConfigForm(formType, config, invalidConfig) {
        cy.findByRole("button", { name: formType }).click();
        cy.findByRole("dialog").should("be.visible");
        cy.updateForm(config);
        if (invalidConfig) {
            cy.findAllByText("Value must be a number").should("be.visible");
        }
        cy.findByRole("dialog").within(() => {
            cy.findByRole("button", { name: "Close" }).click();
        });
        cy.findByRole("dialog").should("not.exist");
    }

    it(
        "Verify that a form can not be submitted when invalid characters are entered in the number field.",
        { tags: ["regression", "config"] },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopRightNav("Configuration", "System");
            cy.findByText("Entity Resolution").click();
            cy.retryTextNotContainCheck(".alert", "Fetching Entity Management configuration...");
            updateEntityConfigForm("Device", invalidDeviceEntityConfig, true);
            updateEntityConfigForm("User", { "User Age Out Time *": "Databee-User" }, true);
            cy.updateForm({ "Owner Inference Wait Period *": "DatabeeEnable" });
            cy.findByText("Value must be a number").should("be.visible");
            cy.findByRole("button", { name: "Force Owner Inference" }).should("be.enabled");
            cy.findByRole("button", { name: "Submit" }).should("be.disabled");
        }
    );

    it(
        "Verify that admin can uncheck the enable owner inference checkbox and submit button is enabled.",
        { tags: ["regression", "config"] },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopRightNav("Configuration", "System");
            cy.findByText("Entity Resolution").click();
            cy.retryTextNotContainCheck(".alert", "Fetching Entity Management configuration...");
            cy.updateForm({ "Enable Owner Inference *": "false" });
            cy.findByRole("button", { name: "Submit" }).should("be.enabled");
        }
    );

    it(
        "Verify that admin can provide owner wait period and running the force owner inference",
        { tags: ["regression", "config"] },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopRightNav("Configuration", "System");
            cy.findByText("Entity Resolution").click();
            cy.retryTextNotContainCheck(".alert", "Fetching Entity Management configuration...");
            cy.updateForm({ "Owner Inference Wait Period *": 2 });
            cy.findByRole("button", { name: "Force Owner Inference" }).click();
            cy.findByRole("dialog").within(() => {
                cy.findByRole("button", { name: "Force Owner Inference" }).click();
            });
            cy.findByText("Running Owner Inference").should("be.visible");
        }
    );

    it("Verify that a form can be submitted with valid update details.", { tags: ["smoke", "config"] }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopRightNav("Configuration", "System");
        cy.findByText("Entity Resolution").click();
        cy.retryTextNotContainCheck(".alert", "Fetching Entity Management configuration...");
        const jsonObject = { "OCSF Activity Inclusion List *": "HTTP Activity (4002)" };
        Object.values(jsonObject).forEach((value) => {
            cy.deleteValueFromMultiSelectField(value);
        });
        cy.updateForm(jsonObject);
        updateEntityConfigForm("Device", validDeviceEntityConfig);
        updateEntityConfigForm("User", { "User Age Out Time *": 300 });
        cy.findByRole("button", { name: "Submit" }).click();
        cy.findByText("Configuration updated successfully.").should("be.visible");
    });

    it(
        "Verify that admin can clear all OCSF inclusion lists and error message. ",
        { tags: ["regression", "config"] },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopRightNav("Configuration", "System");
            cy.findByText("Entity Resolution").click();
            cy.retryTextNotContainCheck(".alert", "Fetching Entity Management configuration...");
            cy.get('[class*="react-select__clear-indicator"]').eq(0).click();
            cy.findByText("Required!").should("be.visible");
            cy.findByRole("button", { name: "Submit" }).should("be.disabled");
        }
    );
});
