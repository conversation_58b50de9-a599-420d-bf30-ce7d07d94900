const moment = require("moment");

const timeUnits = ["Seconds", "Minutes", "Hours", "Days", "Weeks", "Months"];

const accessActiveDetectionsResults = () => {
    cy.clickViewDetails("Actions", "View Detection Details", "title");
    cy.findByText("Detection Details").should("be.visible");
    cy.get('[class="modal-body"]').should("be.visible");
    cy.findByLabelText("Close").click();
};

const validateActiveDetectionStreamContent = () => {
    cy.contains("h2", "Active Detection Streams").should("be.visible");

    cy.get('[id="sigma-rules-results"]').contains("No Data Available").should("not.exist");

    cy.get('[id="sigma-rules-results"]')
        .find("thead")
        .within(() => {
            cy.get("th").eq(0).should("contain", "Title");
            cy.get("th").eq(1).should("contain", "Level");
            cy.get("th").eq(2).should("contain", "Status");
            cy.get("th").eq(3).should("contain", "Log Source Product");
            cy.get("th").eq(4).should("contain", "Category");
            cy.get("th").eq(5).should("contain", "Tags");
            cy.get("th").eq(6).should("contain", "Created Date");
            cy.get("th").eq(7).should("contain", "Details");
        });
};

const performActiveDetectionStreamFilter = (controlId, option) => {
    cy.get('[id="sigma-rules"]')
        .find("p")
        .within(() => {
            cy.findByText("Filter Parameters:").should("be.visible");
        });
    cy.get('[id="sigma-rules"]')
        .find("div")
        .first()
        .within(() => {
            cy.get(`[id="basic-filter-${controlId}"]`).click();
            cy.get(`[id="filter-in-${controlId}"]`).click();
            cy.contains(".react-select__option", option).click();
        });
    cy.get("body").click(0, 0);
    cy.findByRole("button", { name: "Apply" }).click();
    const columnName = controlId.charAt(0).toUpperCase() + controlId.slice(1);

    cy.findByRole("table")
        .find("th")
        .filter(`:contains("${columnName}")`)
        .invoke("index")
        .then((columnIndex) => {
            cy.findByRole("table")
                .find("tbody")
                .find("tr")
                .each(($row) => {
                    cy.wrap($row).find("td").eq(columnIndex).should("contain", option);
                });
        });
    cy.findByRole("button", { name: "Reset" }).click();
};

const getUtcDateTime = (addDay) => {
    const utcDateTime = moment().utc().add(addDay, "day").format("MM/DD/YYYY h:mm A");
    const utcStringFormat = utcDateTime.substring(0, utcDateTime.length - 1);
    return utcStringFormat;
};

const setReactInputs = (key, val) => {
    cy.findByLabelText(key).should("be.exist");
    cy.get('[class*="react-select__input"]')
        .find(`[id="${key.toLowerCase()}"]`)
        .focus()
        .type(val)
        .get(".react-select__menu.css-1nmdiq5-menu")
        .find('div[tabindex*="-1"]')
        .first()
        .click();
};

const setReactSelect = (key, val) => {
    cy.findByLabelText(key).should("be.exist");
    cy.get('[class*="react-select__input"]')
        .find(`[id="${key.toLowerCase()}"]`)
        .focus()
        .type(val)
        .type("{enter}", { force: true });
};

const addSuppression = (suppInputs) => {
    cy.findByText("Add Suppression").click();
    cy.contains("Add Suppression").should("be.visible");
    if (suppInputs.Action) {
        cy.findByLabelText("Action").should("be.exist");
        cy.get('[class*="react-select__input"]')
            .findByRole("combobox")
            .focus()
            .type(suppInputs.Action)
            .type("{enter}", { force: true });
    }
    if (suppInputs.Users) {
        setReactInputs("Users", suppInputs.Users);
    }
    if (suppInputs.Devices) {
        setReactInputs("Devices", suppInputs.Devices);
    }
    if (suppInputs.Features) {
        setReactSelect("Features", suppInputs.Features);
    }
    if (suppInputs.Products) {
        setReactSelect("Products", suppInputs.Products);
    }
    if (suppInputs["Finding Titles"]) {
        cy.findByLabelText("Finding Titles").should("be.exist");
        cy.get('[class*="react-select__input"]')
            .find('[id="titles"]')
            .focus()
            .type(suppInputs["Finding Titles"])
            .type("{enter}", { force: true });
    }
    if (suppInputs["Analytic Names"]) {
        cy.findByLabelText("Analytic Names").should("be.exist");
        cy.get('[class*="react-select__input"]')
            .find('[id="analytics"]')
            .focus()
            .type(suppInputs["Analytic Names"])
            .type("{enter}", { force: true });
    }
    if (suppInputs.Description) {
        setReactSelect("Description", suppInputs.Description);
    }
    if (suppInputs.Severity_Ids) {
        cy.findByLabelText("Severity Ids").should("be.exist");
        cy.get('[class*="react-select__input"]')
            .find('[id="severity-ids"]')
            .focus()
            .type(suppInputs.Severity_Ids)
            .type("{enter}", { force: true });
    }
    if (suppInputs.Rule_UIDs) {
        cy.findByLabelText("Rule UIDs").should("be.exist");
        cy.get('[class*="react-select__input"]')
            .find('[id="rule-uids"]')
            .focus()
            .type(suppInputs.Rule_UIDs)
            .type("{enter}", { force: true });
    }
    if (suppInputs.End_Suppress_On) {
        cy.findByLabelText("End Suppress On").should("be.exist");
        const utcTime = getUtcDateTime(suppInputs.End_Suppress_On);
        cy.get('[id="end_suppress_on"]').type(utcTime).type("{enter}", { force: true });
    }
    if (suppInputs.Schedule) {
        cy.findByLabelText("Weekdays").should("be.exist");
        cy.get('[class*="react-select__input"]')
            .find('[id="weekday"]')
            .focus()
            .type(suppInputs.Schedule.weekdays)
            .type("{enter}", { force: true });
        cy.findByLabelText("Start Hour").should("be.exist");
        cy.get('[id="start-hour"]').type(suppInputs.Schedule.Start_Hour, { force: true });
        cy.findByLabelText("Stop Hour").should("be.exist");
        cy.get('input[id="stop-hour"]').type(suppInputs.Schedule.Stop_Hour, { force: true });
    }
    cy.findByRole("button", { name: "Create" }).click();
    cy.validateToastMessage("1 records created successfully.");
};

const editSuppression = (suppInputs) => {
    cy.findByRole("table")
        .find("tbody")
        .find("tr")
        .first()
        .find("td")
        .last()
        .within(() => {
            cy.findByTitle("Edit Suppress List").click();
        });
    cy.findByText("Update Suppression").should("be.visible");
    if (suppInputs.Users) {
        setReactInputs("Users", suppInputs.Users);
    }
    if (suppInputs.Devices) {
        setReactInputs("Devices", suppInputs.Devices);
    }
    if (suppInputs.Features) {
        setReactSelect("Features", suppInputs.Features);
    }
    if (suppInputs.Products) {
        setReactSelect("Products", suppInputs.Products);
    }
    if (suppInputs.Description) {
        cy.get('[id="description"]').type(suppInputs.Description, { force: true });
    }
    if (suppInputs.Severity_Ids) {
        cy.findByLabelText("Severity Ids").should("be.exist");
        cy.get('[class*="react-select__input"]')
            .find('[id="severity-ids"]')
            .focus()
            .type(suppInputs.Severity_Ids)
            .type("{enter}", { force: true });
    }
    if (suppInputs.Rule_UIDs) {
        cy.findByLabelText("Rule UIDs").should("be.exist");
        cy.get('[class*="react-select__input"]')
            .find('[id="rule-uids"]')
            .focus()
            .type(suppInputs.Rule_UIDs)
            .type("{enter}", { force: true });
    }
    cy.findByRole("button", { name: "Update" }).click();
    cy.contains("Suppression updated").should("be.visible");
};

const deleteSuppression = () => {
    cy.findByRole("table")
        .find("tbody")
        .find("tr")
        .first()
        .find("td")
        .last()
        .within(() => {
            cy.findByRole("button", { name: "Delete Suppress List" }).click();
        });
    cy.findByText("Do you wish to continue?").should("be.visible");
    cy.findByRole("button", { name: "Yes" }).click();
    cy.contains("Suppression Deleted").should("be.visible");
};

const accessSavedSearch = (queryName, searchParams) => {
    const fieldKey = Object.keys(searchParams)[0];
    const fieldValue = searchParams[fieldKey];

    // Extract the time unit and its value dynamically
    const timeUnitKey = timeUnits.find((unit) => Object.prototype.hasOwnProperty.call(fieldValue, unit));
    const timeUnitValue = fieldValue[timeUnitKey];
    cy.findByText("Saved Searches").click();
    cy.findByText("Filter:").click().type(queryName);
    cy.findByRole("cell", { name: `${queryName}` }).should("be.visible");
    cy.findByText(
        `time last ${timeUnitValue} ${timeUnitKey.toLowerCase()} and metadata.product.feature.name in sigma_engine,detection_chaining`
    ).should("be.visible");
    cy.findByRole("button", { name: "Execute" }).click();
    cy.contains(`${fieldKey} Last ${timeUnitValue} ${timeUnitKey.toLowerCase()}`).should("be.visible");
    cy.findByRole("button", { name: "Search" }).click();
    cy.clickViewDetails("Actions", "View Detection Finding Details", "title");
    cy.findByText("Detection Finding Details").should("be.visible");
    cy.get('[class="modal-body"]').should("be.visible");
    cy.findByLabelText("Close").click();
};

const accessSearchHistory = (searchParams) => {
    const fieldKey = Object.keys(searchParams)[0];
    const fieldValue = searchParams[fieldKey];

    // Extract the time unit and its value dynamically
    const timeUnitKey = timeUnits.find((unit) => Object.prototype.hasOwnProperty.call(fieldValue, unit));
    const timeUnitValue = fieldValue[timeUnitKey];
    cy.findByText("Search History").click();
    cy.findByText("Filter:")
        .click()
        .type(
            `time last ${timeUnitValue} ${timeUnitKey.toLowerCase()} and metadata.product.feature.name in sigma_engine,detection_chaining`
        );
    cy.get("table.rt-table").find("tbody").find("tr").contains("Detection Finding").should("be.visible");
    cy.get("table.rt-table")
        .find("tbody")
        .find("tr")
        .contains(
            `time last ${timeUnitValue} ${timeUnitKey.toLowerCase()} and metadata.product.feature.name in sigma_engine,detection_chaining`
        )
        .should("be.visible");
    cy.get("table.rt-table").find("tbody").find("tr").contains("Execute").click();
    cy.findByText("Findings will be displayed for DataBee product only.").should("be.visible");
    cy.contains(`${fieldKey} Last ${timeUnitValue} ${timeUnitKey.toLowerCase()}`).should("be.visible");
    cy.findByRole("button", { name: "Search" }).click();
    cy.clickViewDetails("Actions", "View Detection Finding Details", "title");
    cy.findByText("Detection Finding Details").should("be.visible");
    cy.get('[class="modal-body"]').should("be.visible");
    cy.findByLabelText("Close").click();
};

const clickSaveAsDefault = () => {
    cy.findByRole("button", { name: "Save as default" }).click();
    cy.checkElementIsVisible(".modal.fade.show", 20);
    cy.findByRole("dialog", { timeout: 60000 }) // Increase timeout to 60 seconds
        .should("be.visible")
        .within(() => {
            // Look for the heading inside the modal
            cy.findByRole("heading", { name: "Save as default query" }).should("be.visible");

            // Find and click the Yes button
            cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
        });
};

const verifySearchParamsValue = (fieldValue, value) => {
    cy.findByText(fieldValue, { exact: false }).invoke("text").as("getByText");

    const verifyFilterTime = (actualValue, key, actual) => {
        const expectedValue = `${fieldValue} Last ${actual} ${key.toLowerCase()}`;
        if (actual === 1) {
            const adjustedField = expectedValue.trim().replace(/s$/, "");
            expect(actualValue).to.equal(adjustedField);
        } else {
            expect(actualValue).to.equal(expectedValue);
        }
    };

    const verifyInNotIn = (actualValue, key, actual) => {
        const expectedValue = `${fieldValue} ${key.toLowerCase()} (${actual})`;
        if (key === "In") {
            expect(expectedValue).to.include(actualValue);
        } else if (key === "NotIn") {
            expect(actualValue).to.include(expectedValue);
        }
    };

    Object.entries(value).forEach(([key, actual]) => {
        cy.get("@getByText").then((actualValue) => {
            if (timeUnits.includes(key)) {
                verifyFilterTime(actualValue, key, actual);
            } else {
                verifyInNotIn(actualValue, key, actual);
            }
        });
    });
};

const formatSearchParams = (searchParams) =>
    Object.entries(searchParams)
        .map(([key, value]) => {
            const [unit, number] = Object.entries(value)[0];
            let formattedKey = key.replace(":", "").toLowerCase().replace(" ", "_");

            if (key !== "Modified Time:") {
                formattedKey = "time";
            }

            return `${formattedKey} last ${number} ${unit.toLowerCase()}`;
        })
        .join(" ");

const verifyEditDetectionChain = (formData, tableName, searchParams) => {
    Object.entries(formData).forEach(([field, value]) => {
        cy.verifyInputValue(field, value);
    });
    // Format the search parameters
    const formattedSearchParams = formatSearchParams(searchParams);
    const table = tableName.toLowerCase().replace(/ /g, "_");
    const expectedValue = `${table}: ${formattedSearchParams}`;

    cy.verifyTableContentWithCondition({
        modelSelector: ".modal-body",
        tableSelector: "table",
        noDataText: "No Data Available",
        actions: {
            onTextFound: () => {
                cy.findByText("No Data Available").should("be.visible");
            },
            onTextNotFound: () => {
                cy.contains("tr", expectedValue)
                    .should("exist")
                    .then(() => {
                        cy.findByRole("button", { name: "Delete" }).should("not.be.disabled");
                        cy.findByRole("button", { name: "Edit" }).should("not.be.disabled").click();
                    });
                cy.contains("tr", expectedValue)
                    .should("exist")
                    .then(() => {
                        cy.findAllByText("Cancel", {
                            selector: ".btn-secondary",
                        }).should("not.be.disabled");
                        cy.findByRole("button", { name: "Delete" }).should("not.exist");
                    });
            },
        },
    });
    cy.verifyInputValue("Create Link For:", tableName);
    cy.verifyInputValue("Minimum Event Count:", "1");
    cy.findByRole("button", { name: /Update Link/i }).should("not.be.disabled");
    cy.findAllByText("Save Chain", {
        selector: ".btn.btn-primary",
    }).should("not.be.disabled");
    cy.findAllByText("Cancel", {
        selector: ".btn.btn-secondary",
    }).should("not.be.disabled");
    cy.contains("tr", expectedValue)
        .should("exist")
        .then(() => {
            cy.findAllByText("Cancel", {
                selector: ".mr-2.btn.btn-secondary.btn-sm",
            })
                .should("not.be.disabled")
                .click();
        });
    cy.findByRole("button", { name: /Add Link/i }).should("not.be.disabled");
};

export {
    accessActiveDetectionsResults,
    accessSavedSearch,
    accessSearchHistory,
    addSuppression,
    clickSaveAsDefault,
    deleteSuppression,
    editSuppression,
    formatSearchParams,
    getUtcDateTime,
    performActiveDetectionStreamFilter,
    setReactInputs,
    validateActiveDetectionStreamContent,
    verifyEditDetectionChain,
    verifySearchParamsValue,
};
