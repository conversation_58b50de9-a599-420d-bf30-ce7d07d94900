/// <reference types="cypress"/>
import {
    createSaveSearches,
    deleteSavedSearches,
    removeSelectValues,
    setSearchParams,
    setSearchTable,
} from "../../iris/search/search_utils";

import { accessActiveDetectionsResults, formatSearchParams, verifyEditDetection<PERSON>hain } from "./security_utils";

describe("Tenant: Security -> Active Detections", { tags: ["security", "full", "data_faker"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });
    const randomString = Math.random().toString(36).substring(8);
    const titleName = `Chain-${randomString}`;

    const searchParams = {
        "Event Time:": { Months: 24 },
    };

    it("<PERSON><PERSON> can apply and reset the search filters", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Security", "Detection Streams");
        setSearchParams({ "Created Date:": { Months: 24 } });
        cy.findByRole("button", { name: "Apply" }).click();
        cy.verifyTableContentWithCondition({
            tableSelector: "table",
            noDataText: "No Data Available",
            actions: {
                onTextFound: () => {
                    cy.findByText("No Data Available").should("be.visible");
                },
                onTextNotFound: () => {
                    accessActiveDetectionsResults();
                },
            },
        });
        cy.findByRole("button", { name: "Reset" }).click();
        cy.findAllByText("Created Date: All Time").should("be.exist");
    });

    it("Admin can create, edit and delete detection chains with link configurations", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Security", "Detection Chains");
        cy.findByRole("button", { name: "Create Detection Chain" }).click();
        cy.findAllByRole("button", { name: /save chain/i }).should("be.disabled");
        const validChainForm = {
            "Title *": titleName,
            "Status *": "Test",
            "Severity *": "High",
            Description: "To view the information chain links",
            Tags: "Detect_Chain",
            "Run Frequency": 12,
        };
        cy.updateForm(validChainForm);
        cy.findByText("No Links added to this Detection Chain yet.").should("be.visible");
        const searchTable = "SSH Activity";
        setSearchTable("Create Link For:", searchTable);
        setSearchParams(searchParams);
        cy.setInput("Minimum Event Count:", "1");
        cy.findByRole("button", { name: "Add Link" }).click();
        cy.findByText("Save Chain").click();
        cy.validateToastMessage("Detection Chain created");

        // verify and edit the detection chain
        setSearchParams({
            "Title:": { In: [titleName] },
        });
        cy.findByRole("button", { name: "Apply" }).click();
        cy.clickViewDetails("Actions", "Edit Detection Chain", "title");
        verifyEditDetectionChain(validChainForm, searchTable, searchParams);
        const updateTitleName = `Chain-${randomString}`;
        const updateChainForm = {
            "Title *": updateTitleName,
            Description: "To update the information chain links",
            "Run Frequency": 14,
        };
        cy.updateForm(updateChainForm);
        const editSearchTable = "User Inventory Info";
        setSearchTable("Create Link For:", editSearchTable);
        setSearchParams({
            "Event Time:": { Months: 2 },
        });
        cy.findByRole("button", { name: "Add Link" }).click();
        cy.findByText("Save Chain").click();
        cy.validateToastMessage("Detection Chain updated");

        // Delete the detection chain
        setSearchParams({
            "Title:": { In: [updateTitleName] },
        });
        cy.findByRole("button", { name: "Apply" }).click();
        cy.clickViewDetails("Actions", "Delete Detection Chain", "title");
        cy.checkElementIsVisible(".modal.fade.show", 30);
        cy.findByRole("dialog", { timeout: 60000 })
            .should("be.visible")
            .within(() => {
                // Look for the heading inside the modal
                cy.findByRole("heading", { name: "Confirm Delete" }).should("be.visible");

                // Find and click the Yes button
                cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
            });

        cy.validateToastMessage("Detection Chain deleted");
    });

    it(
        "Admin can verify that a form can not be submitted when title is not entered in the field.",
        { tags: "regression" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Security", "Detection Chains");
            cy.findByRole("button", { name: "Create Detection Chain" }).click();
            cy.findAllByRole("button", { name: /save/i }).should("be.disabled");
            const invalidChainForm = {
                "Status *": "Test",
                "Severity *": "High",
            };
            cy.updateForm(invalidChainForm);
            cy.findByText("No Links added to this Detection Chain yet.").should("be.visible");
            setSearchTable("Create Link For:", "Device Config State");
            setSearchParams(searchParams);
            cy.findByRole("button", { name: "Add Link" }).click();
            cy.findByLabelText("Title *")
                .should("have.class", "is-invalid")
                .siblings("small")
                .should("have.text", "The name of this detection chain");
            cy.findByText("Save Chain").should("be.disabled");
        }
    );

    it("Admin can verify Creating and Using a Saved Search in the Detection Chains.", { tags: "smoke" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert", " Loading tables...");
        const { selectList, queryName, searchTimeParam } = createSaveSearches(
            {
                "Event Time:": { Months: 2 },
            },
            "Authentication"
        );
        cy.navigateTopNav("Security", "Detection Chains");
        cy.findByRole("button", { name: "Create Detection Chain" }).click();
        cy.findAllByRole("button", { name: /save/i }).should("be.disabled");
        const chainForm = {
            "Title *": titleName,
            "Status *": "Test",
            "Severity *": "High",
        };
        cy.updateForm(chainForm);
        cy.findByText("No Links added to this Detection Chain yet.").should("be.visible");
        cy.findByText("From Saved Search").click();
        cy.setInput("Filter:", queryName);
        cy.findByRole("dialog").within(() => {
            cy.findByText(selectList).should("be.visible");
            cy.findByRole("cell", { name: `${queryName}` }).should("be.visible");
            const formattedSearchParams = formatSearchParams(searchTimeParam);
            cy.findByText(`${formattedSearchParams}`).should("be.visible");
            cy.findByRole("button", { name: "Load" }).click();
        });
        cy.findByText("Save Chain").click();
        cy.validateToastMessage("Detection Chain created");

        // Delete the detection chain
        setSearchParams({
            "Title:": { In: [titleName] },
        });
        cy.findByRole("button", { name: "Apply" }).click();
        cy.clickViewDetails("Actions", "Delete Detection Chain", "title");
        cy.checkElementIsVisible(".modal.fade.show", 30);
        cy.findByRole("dialog", { timeout: 60000 })
            .should("be.visible")
            .within(() => {
                // Look for the heading inside the modal
                cy.findByRole("heading", { name: "Confirm Delete" }).should("be.visible");

                // Find and click the Yes button
                cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
            });
        cy.validateToastMessage("Detection Chain deleted");

        // Delete the saved searches from search page
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert", "Loading tables...");
        deleteSavedSearches(queryName);
    });

    it(
        "Admin can verify Creating and Using a Saved Search from the Databee Findings Page in Detection Chains.",
        { tags: "regression" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Security", "DataBee Findings");
            cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");
            removeSelectValues("Activity ID:", ["Create", "Unknown"]);
            removeSelectValues("Type ID:", ["Detection Finding: Unknown"]);
            const databeeSearchParams = {
                "Event Time:": { Months: 2 },
            };
            const { queryName } = createSaveSearches(databeeSearchParams);
            cy.findByRole("button", { name: "Create Detection Chain" }).click();
            cy.findAllByRole("button", { name: /save chain/i }).should("be.disabled");
            const chainForm = {
                "Title *": titleName,
                "Status *": "Test",
                "Severity *": "High",
            };
            cy.updateForm(chainForm);
            cy.findByText("No Links added to this Detection Chain yet.").should("be.visible");
            cy.findByText("From Saved Search").click();
            cy.setInput("Filter:", queryName);
            cy.findByRole("dialog").within(() => {
                cy.findByText("Detection Finding").should("be.visible");
                cy.findByRole("cell", { name: `${queryName}` }).should("be.visible");
                const fieldKey = Object.keys(databeeSearchParams)[0];
                const fieldValue = databeeSearchParams[fieldKey];

                const timeUnits = ["Seconds", "Minutes", "Hours", "Days", "Weeks", "Months"];
                // Extract the time unit and its value dynamically
                const timeUnitKey = timeUnits.find((unit) => Object.prototype.hasOwnProperty.call(fieldValue, unit));
                const timeUnitValue = fieldValue[timeUnitKey];
                cy.findByText(
                    `time last ${timeUnitValue} ${timeUnitKey.toLowerCase()} and metadata.product.feature.name in sigma_engine,detection_chaining`
                ).should("be.visible");
                cy.findByRole("button", { name: "Load" }).click();
            });
            cy.findByText("Save Chain").click();
            cy.findByText("Detection Chain created").should("be.visible");

            // Delete the detection chain
            cy.navigateTopNav("Security", "Detection Chains");
            setSearchParams({
                "Title:": { In: [titleName] },
            });
            cy.findByRole("button", { name: "Apply" }).click();
            cy.clickViewDetails("Actions", "Delete Detection Chain", "title");
            cy.checkElementIsVisible(".modal.fade.show", 30);
            cy.findByRole("dialog", { timeout: 60000 })
                .should("be.visible")
                .within(() => {
                    // Look for the heading inside the modal
                    cy.findByRole("heading", { name: "Confirm Delete" }).should("be.visible");

                    // Find and click the Yes button
                    cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
                });
            cy.findByText("Detection Chain deleted").should("be.visible");

            // Delete the saved searches from databee findings page
            cy.navigateTopNav("Security", "DataBee Findings");
            cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");
            deleteSavedSearches(queryName);
        }
    );

    it(
        "Admin can confirm the successful editing of detection chains without encountering any errors on the page.",
        { tags: ["demo_staging"] },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Security", "Detection Chains");
            setSearchParams({
                "Title:": { In: ["Potential Insider Threat"] },
            });
            cy.findByRole("button", { name: "Apply" }).click();
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading...");
            cy.clickViewDetails("Actions", "Edit Detection Chain", "title");
            cy.findByText("Edit Detection Chain").should("be.visible");
            cy.findByText("Links").should("be.visible");
            cy.findByRole("button", { name: /From Saved Search/i }).should("be.visible");
            cy.findByRole("button", { name: /From Existing Link/i }).should("be.visible");
            // Verify table has the correct structure and content
            cy.findByRole("columnheader", { name: /Link/i }).should("be.visible");
            cy.findByRole("columnheader", { name: /Minimum Event Count/i }).should("be.visible");
            cy.findByRole("columnheader", { name: /Actions/i }).should("be.visible");

            // Check if the "Edit" and "Delete" buttons in the Actions column are present and visible
            cy.findAllByRole("button", { name: /Edit/i }).eq(1).should("be.visible");
            cy.findAllByRole("button", { name: /Delete/i })
                .eq(1)
                .should("be.visible");
            cy.findByRole("button", { name: "Add Link" }).should("be.enabled");
            cy.findByText("Save Chain").should("be.enabled");
        }
    );

    it(
        "Admin verifies configured Active Detection statuses are applied on the Security → Active Detections page",
        { tags: "regression" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );

            cy.navigateTopRightNav("Configuration", "Security");
            cy.retryTextNotContainCheck(".alert", "Fetching Sigma Detections configuration...");

            cy.findByRole("link", { name: "Active Detections" }).should("be.visible");

            const selectedValues = [];
            cy.get(".react-select__value-container")
                .first()
                .find(".react-select__multi-value__label")
                .each(($el) => {
                    selectedValues.push($el.text().trim());
                })
                .then(() => {
                    const expectedText = `Status: in (${selectedValues.join(",")})`;

                    cy.navigateTopNav("Security", "Detection Streams");

                    cy.findByText(/Status:/)
                        .should("exist")
                        .invoke("text")
                        .should("be.equal", expectedText);
                });
        }
    );

    // Create Detection Chain
    const createDetectionChain = () => {
        cy.findByRole("button", { name: "Create Detection Chain" }).click();
        cy.findAllByRole("button", { name: /save/i }).should("be.disabled");

        const validChainForm = {
            "Title *": titleName,
            "Status *": "Test",
            "Severity *": "High",
            Description: "To view the information chain links",
            Tags: "Detect_Chain",
            "Run Frequency": 12,
        };
        cy.updateForm(validChainForm);

        // Add pass parameter
        cy.findByText("No Links added to this Detection Chain yet.").should("be.visible");
        const passedParam = "activity_id";
        const parameterName = "testId";
        cy.setInput("Create Passparameter:", passedParam);
        cy.setInput("Parameter Name:", parameterName);
        cy.findByRole("button", { name: "Add Link" }).click();
        cy.findByText("Save Chain").click();
        cy.findByText("Detection Chain created").should("be.visible");

        return { passedParam, parameterName };
    };

    // Edit Detection Chain
    const editDetectionChain = (parameterName, passedParam) => {
        setSearchParams({
            "Title:": { In: [titleName] },
        });
        cy.findByRole("button", { name: "Apply" }).click();
        cy.clickViewDetails("Actions", "Edit Detection Chain", "title");

        // Verify existing parameters before edit
        const expectedText = `${parameterName}: ${passedParam}`;
        cy.contains("tr", expectedText)
            .should("exist")
            .then(() => {
                cy.findByRole("button", { name: "Delete" }).should("not.be.disabled");
                cy.findByRole("button", { name: "Edit" }).should("not.be.disabled").click();
            });

        // Update chain details
        const updateTitleName = `Chain-${randomString}`;
        const updateChainForm = {
            "Title *": updateTitleName,
            Description: "To update the information chain links",
            "Run Frequency": 14,
            "Create Passparameter:": "domain",
            "Parameter Name:": "local",
        };
        cy.updateForm(updateChainForm);
        cy.findByRole("button", { name: "Update Link" }).click();
        cy.findByText("Save Chain").click();
        cy.findByText("Detection Chain updated").should("be.visible");

        return updateTitleName;
    };

    // Delete Detection Chain
    const deleteDetectionChain = (chainTitle) => {
        setSearchParams({
            "Title:": { In: [chainTitle] },
        });
        cy.findByRole("button", { name: "Apply" }).click();
        cy.clickViewDetails("Actions", "Delete Detection Chain", "title");
        cy.checkElementIsVisible(".modal.fade.show", 30);
        cy.findByRole("dialog", { timeout: 60000 })
            .should("be.visible")
            .within(() => {
                // Look for the heading inside the modal
                cy.findByRole("heading", { name: "Confirm Delete" }).should("be.visible");

                // Find and click the Yes button
                cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
            });
        cy.findByText("Detection Chain deleted").should("be.visible");
    };

    it(
        "Admin should be able to perform CRUD operations on Detection Chains with passed parameters",
        { tags: "smoke" },
        () => {
            // Login setup
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );

            // Navigate to Detection Chains page
            cy.navigateTopNav("Security", "Detection Chains");

            // Execute CRUD operations
            const { passedParam, parameterName } = createDetectionChain();
            const updatedTitle = editDetectionChain(parameterName, passedParam);
            deleteDetectionChain(updatedTitle);
        }
    );
});
