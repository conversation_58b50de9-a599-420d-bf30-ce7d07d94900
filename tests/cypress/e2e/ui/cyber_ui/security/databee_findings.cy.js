import {
    addTableColumn,
    checkSavedSearchesOverlay,
    checkSearchHistoryOverlay,
    clickSearch,
    createSaveSearches,
    deleteSavedSearches,
    exportSearchResults,
    removeSelectValues,
    removeTableColumn,
    setColumnVisibility,
    setSearchParams,
    setSearchTable,
    toggleAllColumnVisibilitiesFromDropdown,
} from "../../iris/search/search_utils";

import { accessSavedSearch, accessSearchHistory, clickSaveAsDefault, verifySearchParamsValue } from "./security_utils";

describe("Tenant: Security -> DataBee Findings", { tags: ["security", "data_faker"] }, () => {
    const searchParams = {
        "Event Time:": { Months: 2 },
    };

    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    after(() => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Security", "DataBee Findings");
        setSearchParams(searchParams);
        clickSearch();
        cy.findByText("Customize").click();
        cy.findByRole("button", { name: "Reset to Default" }).click();
        cy.contains("Column layout reset to default");
        cy.clickLogout();
    });

    const randomString = Math.random().toString(36).substring(8);
    const titleName = `Chain-${randomString}`;

    it("Admin user can create a saved searches and access the saved details", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Security", "DataBee Findings");
        cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");
        checkSavedSearchesOverlay();
        removeSelectValues("Activity ID:", ["Create", "Unknown"]);
        removeSelectValues("Type ID:", ["Detection Finding: Unknown"]);
        const { queryName } = createSaveSearches(searchParams);
        accessSavedSearch(queryName, searchParams);
        deleteSavedSearches(queryName);
    });

    it("Admin user can create a search history and access the search history", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Security", "DataBee Findings");
        cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");
        checkSearchHistoryOverlay();
        setSearchParams(searchParams);
        clickSearch();
        accessSearchHistory(searchParams);
    });

    it("Admin user can create a save default query", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Security", "DataBee Findings");
        cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");
        removeSelectValues("Activity ID:", ["Create", "Unknown"]);
        removeSelectValues("Type ID:", ["Detection Finding: Unknown"]);
        clickSearch();
        clickSaveAsDefault();
        cy.validateToastMessage("Search configuration updated.");
        const searchMultiParams = {
            "Event Time:": { Months: 4 },
            "Activity ID:": { In: ["Create", "Unknown"] },
            "Type ID:": { NotIn: ["Detection Finding: Unknown"] },
        };
        setSearchParams(searchMultiParams);
        clickSearch();
        clickSaveAsDefault();
        cy.validateToastMessage("Search configuration updated.");
        Object.entries(searchMultiParams).forEach(([field, value]) => {
            verifySearchParamsValue(field, value);
        });
    });

    it(
        "Admin can access customize functionality, toggle column visibility and verify download functionality",
        { tags: "regression" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Security", "DataBee Findings");
            cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");

            const columnName = "Type ID";
            const searchTable = "Detection Finding";

            setSearchParams(searchParams);
            clickSearch();
            setColumnVisibility(columnName, false);
            setSearchParams(searchParams);
            clickSearch();
            // verify added customize column should not be visiable
            cy.findAllByText(columnName).should("not.exist");

            setColumnVisibility(columnName, true);
            setSearchParams(searchParams);
            clickSearch();
            // verify added customize column should be visiable
            cy.findAllByText(columnName).should("exist");

            toggleAllColumnVisibilitiesFromDropdown();

            // verify Downloads dropdown
            exportSearchResults(searchTable, "json");
            exportSearchResults(searchTable, "csv");
        }
    );

    it("Admin can add and remove column", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Security", "DataBee Findings");
        cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");
        const columnName = "Category";
        setSearchParams(searchParams);
        clickSearch();
        // add customize column
        addTableColumn(columnName);

        setSearchParams(searchParams);
        clickSearch();
        // verify added customize column should be visiable
        cy.findAllByText(columnName).should("exist");

        // remove customize column
        removeTableColumn(columnName);
        setSearchParams(searchParams);
        clickSearch();
        // verify removed customize column should be not visiable
        cy.findAllByText(columnName).should("not.exist");
    });

    it("Admin can create new detection chains", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Security", "DataBee Findings");
        cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");
        cy.findByRole("button", { name: "Create Detection Chain" }).click();
        cy.findByText("Create Detection Chain").should("be.visible");
        cy.findAllByRole("button", { name: /save/i }).should("be.disabled");
        const validChainForm = {
            "Title *": titleName,
            "Status *": "Test",
            "Severity *": "High",
            Description: "To view the information chain links",
            Tags: "Detect_Chain",
            "Run Frequency": 12,
        };
        cy.updateForm(validChainForm);
        cy.findByText("No Links added to this Detection Chain yet.").should("be.visible");
        setSearchTable("Create Link For:", "Detection Finding");
        cy.setInput("Minimum Event Count:", 2);
        const searchParam = {
            "Event Time:": { Months: 2 },
        };
        setSearchParams(searchParam);
        cy.findByRole("button", { name: "Add Link" }).click();
        cy.findByText("Save Chain").click();
        cy.findByText("Detection Chain created").should("be.visible");

        // delete the detection chain
        cy.navigateTopNav("Security", "Detection Chains");
        const filterParams = {
            "Title:": { In: [titleName] },
        };
        setSearchParams(filterParams);
        cy.findByRole("button", { name: "Apply" }).click();
        cy.clickViewDetails("Actions", "Delete Detection Chain", "title");
        cy.get(".modal.fade.show").should("exist");
        cy.findByRole("dialog", { timeout: 60000 })
            .should("be.visible")
            .within(() => {
                // Look for the heading inside the modal
                cy.findByRole("heading", { name: "Confirm Delete" }).should("be.visible");

                // Find and click the Yes button
                cy.findByRole("button", { name: "Yes" }).should("be.visible").click();
            });
        cy.findByText("Detection Chain deleted").should("be.visible");
    });
});
