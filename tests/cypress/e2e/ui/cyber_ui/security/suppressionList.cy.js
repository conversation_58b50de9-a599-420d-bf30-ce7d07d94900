/// <reference types="cypress"/>

import { clickSearch, setSearchParams, setSearchTable } from "../../iris/search/search_utils";
import { clearAndSelectEventTimelineFilters } from "../entity_view/entity_utils";

import { addSuppression, deleteSuppression, editSuppression } from "./security_utils";

const searchParams = {
    "Event Time:": { Months: 4 },
    "Policy:": { IsNotNone: true },
};

describe("Tenant: Security -> Suppress List", { tags: ["smoke", "security", "full"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    it("Admin user can add, edit and delete the suppression list", { tags: "smoke" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Security", "Suppress List");
        const suppInputs = {
            Action: "Informational",
            Features: "detection_chaining",
            Severity_Ids: "High",
            Rule_UIDs: 121,
            End_Suppress_On: 1,
        };
        addSuppression(suppInputs);
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", "Loading...");
        editSuppression({ Features: "sigma_engine", Description: "Test Log", Severity_Ids: "Unknown" });
        deleteSuppression();
    });
});

describe("Tenant: Security -> Suppress List", { tags: ["regression", "event_suppress", "full", "data_faker"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    it("Admin can enable suppress for a finding event and add it to the suppress list", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Security", "DataBee Findings");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading DataBee Findings...");
        cy.findByText("Add Parameter").closest(".react-select__control").click().type("Policy");
        cy.findByRole("option", { name: "Policy" }).click({
            force: true,
        });
        setSearchParams(searchParams);
        clickSearch();
        cy.findByText("Searching...").should("not.exist");
        cy.clickViewDetails("Device: hostname", "View Details", "title");
        // Wait for any loading to complete and for the label with text "As Of:" to appear
        cy.contains("label", "As Of:", { timeout: 60000 }).should("be.visible");
        // If device is not active, click the button to view details
        cy.get("body").then(($body) => {
            if ($body.find('*:contains(" is not active at this time. It was last seen on")').length) {
                cy.contains(" is not active at this time. It was last seen on").then(() => {
                    cy.get("p:has(button)").find("button").click();
                });
            }
        });
        cy.findByRole("table", { timeout: 60000 }).get("#event-history-table").find("tbody").should("be.visible");
        cy.findByRole("button", { name: "Suppress" }).should("be.disabled");
        clearAndSelectEventTimelineFilters(["All Findings"]);
        cy.findAllByLabelText("Include Events from Device Owner")
            .should("exist")
            .first()
            .scrollIntoView()
            .check({ force: true })
            .should("be.checked");
        cy.findByRole("table").get("#event-history-table").find("tbody").should("be.visible");
        cy.get('[data-testid="virtuoso-item-list"] tr')
            .filter(":visible")
            .each(($row) => {
                // Check if this row has a Suppress Event button
                const hasSuppress = Cypress.$($row).find('[title="Suppress Event"]').length > 0;
                if (hasSuppress) {
                    // Find and click the checkbox in this row
                    cy.wrap($row).find('input[type="checkbox"]').should("exist").click({ force: true });

                    // Verify the checkbox was selected
                    cy.wrap($row).find('input[type="checkbox"]').should("be.checked");
                }
            });
        cy.findByRole("button", { name: "Suppress" }).should("be.enabled").click();
        cy.contains("Loading Suppression Data...").should("be.exist");
        cy.findByRole("dialog").within(() => {
            cy.findByRole("heading").should("contain.text", "Add Suppression");
            cy.findByRole("button", { name: "Create" }).should("be.enabled").click();
        });
        cy.validateToastMessage("1 records created successfully.");
        cy.navigateTopNav("Security", "Suppress List");
        cy.findByRole("link", { name: /suppress list/i }).should("be.visible");
        cy.findByRole("table").find("tbody").find("tr").should("have.length.at.least", 1);
        cy.findByRole("table")
            .find("tbody")
            .find("tr")
            .eq(0)
            .within(() => {
                cy.findByTitle("Delete Suppress List").should("be.enabled").click();
            });
        cy.findByRole("dialog").within(() => {
            cy.findByRole("button", { name: "Yes" }).should("be.enabled").click();
        });
        cy.validateToastMessage("Suppression Deleted");
    });

    it(
        "Admin can suppress a finding event via the search page, add it to the suppression list, and then edit and delete the suppression entry",
        { tags: "regression" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Search");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
            setSearchTable("Search For:", "Detection Finding");
            cy.findByText("Add Parameter").closest(".react-select__control").click().type("Policy");
            cy.findByRole("option", { name: "Policy" }).click({
                force: true,
            });
            setSearchParams(searchParams);
            clickSearch();
            cy.findByText("Searching...").should("not.exist");
            cy.findByText("Loading Histogram...").should("not.exist");
            cy.clickViewDetails("Device: hostname", "View Details", "title");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading...");
            // Wait for any loading to complete and for the label with text "As Of:" to appear
            cy.contains("label", "As Of:", { timeout: 60000 }).should("be.visible");
            // If device is not active, click the button to view details
            cy.get("body").then(($body) => {
                if ($body.find('*:contains(" is not active at this time. It was last seen on")').length) {
                    cy.contains(" is not active at this time. It was last seen on").then(() => {
                        cy.get("p:has(button)").find("button").click();
                    });
                }
            });
            cy.findByRole("table", { timeout: 60000 }).get("#event-history-table").find("tbody").should("be.visible");
            cy.findByRole("table").get("#event-history-table").find("tbody").should("be.visible");
            cy.findByRole("table")
                .find("tbody")
                .find("tr")
                .then(($elements) => {
                    cy.wrap($elements.find("[title='Suppress Event']").first().closest("tr"))
                        .find("input[type='checkbox']")
                        .first()
                        .click();
                    cy.wrap($elements.find("[title='Suppress Event']")).first().click();
                    cy.contains("Loading Suppression Data...").should("be.exist");
                    cy.findByRole("dialog").within(() => {
                        cy.findByRole("heading").should("contain.text", "Add Suppression");
                        cy.findByRole("button", { name: "Create" }).should("be.enabled").click();
                    });
                    cy.validateToastMessage("1 records created successfully.");
                    cy.navigateTopNav("Security", "Suppress List");
                    cy.findByRole("link", { name: /suppress list/i }).should("be.visible");
                    cy.findByRole("table").find("tbody").find("tr").should("have.length.at.least", 1);
                    cy.findByRole("table")
                        .find("tbody")
                        .find("tr")
                        .eq(0)
                        .within(() => {
                            cy.findByTitle("Edit Suppress List").should("be.enabled").click();
                        });
                    cy.findByRole("dialog").within(() => {
                        cy.findByRole("heading").should("contain.text", "Update Suppression");
                        cy.findByRole("button", { name: "Update" }).should("be.enabled");
                        cy.findByRole("button", { name: "Cancel" }).should("be.enabled").click();
                    });
                    cy.get("tbody tr")
                        .first()
                        .within(() => {
                            cy.findByRole("button", { name: "Delete Suppress List" })
                                .should("be.exist")
                                .should("be.enabled")
                                .click({ force: true });
                        });
                    cy.findByRole("dialog").within(() => {
                        cy.findByRole("button", { name: "Yes" }).should("be.enabled").click();
                    });
                    cy.validateToastMessage("Suppression Deleted");
                });
        }
    );
});
