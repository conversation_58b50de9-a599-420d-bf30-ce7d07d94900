const setAsOfDateBackBy = (offset = { days: -1 }) => {
    // Handle legacy support where offset was just days
    const relativeOffset = typeof offset === "number" ? { days: -Math.abs(offset) } : offset;

    // Calculate target date
    const targetDate = new Date();
    if (relativeOffset.days) {
        targetDate.setDate(targetDate.getDate() + relativeOffset.days);
    }
    if (relativeOffset.months) {
        targetDate.setMonth(targetDate.getMonth() + relativeOffset.months);
    }
    if (relativeOffset.years) {
        targetDate.setFullYear(targetDate.getFullYear() + relativeOffset.years);
    }

    // Format date components - using abbreviated month (3 letters)
    const targetMonth = targetDate.toLocaleString("default", { month: "short" }); // "Mar" instead of "March"
    const targetYear = targetDate.getFullYear();
    const targetDay = targetDate.getDate();

    // Open the picker
    cy.get("#as-of-datetime-picker").click();

    // Navigate to date view through month view
    cy.get(".rdtSwitch").click();

    // Now in month view, check if we need to change year
    cy.get(".rdtSwitch").then(($yearSwitch) => {
        const currentYear = $yearSwitch.text().trim();

        if (currentYear !== targetYear.toString()) {
            // Click year to go to year selector
            cy.get(".rdtSwitch").click();

            // Need to select the correct year
            cy.contains(targetYear.toString()).click();
        }
    });

    // Now select the month using the abbreviated format
    cy.contains(targetMonth).click();

    // Finally select the day
    cy.get(".rdtDays .rdtDay")
        .not(".rdtOld")
        .not(".rdtNew")
        .contains(new RegExp(`^${targetDay}$`))
        .click();

    // Click outside to close the picker
    cy.get("body").click("topRight");
};

const selectCurrentTime = () => {
    cy.get("#as-of-datetime-picker").click();
    cy.findByRole("button", { name: "Now" }).click();
};

const getDatePickerValue = () => cy.get("#as-of-datetime-picker").invoke("val");

const checkTableSeverity = ({ allowedSeverities, shouldInclude }) => {
    cy.get("table thead th").then(($headers) => {
        const severityIndex = [...$headers].findIndex((th) => th.textContent === "Severity");
        expect(severityIndex).to.be.greaterThan(-1);

        cy.get("table tbody tr").each(($row) => {
            const severity = $row.find("td").eq(severityIndex).text().trim();
            if (shouldInclude) {
                expect(allowedSeverities).to.include(severity);
            } else {
                expect(allowedSeverities).to.not.include(severity);
            }
        });
    });
};

export { checkTableSeverity, getDatePickerValue, selectCurrentTime, setAsOfDateBackBy };
