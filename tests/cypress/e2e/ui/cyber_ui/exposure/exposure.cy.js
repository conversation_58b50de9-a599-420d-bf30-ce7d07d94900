import { getDatePickerValue, selectCurrentTime, setAsOfDateBackBy } from "./exposure_utils";

describe("Tenant: Exposure -> Entity Detail", { tags: ["exposure", "data_faker"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    it(
        "Admin can verify the entity view details page date selection functionality by selecting a past date and current date for user table",
        { tags: "regression" },
        () => {
            let selectedDate;
            let currentDate;
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Exposure");
            cy.get(".tab.nav.nav-tabs").find(".nav-link").contains("User").click().should("have.class", "active");
            setAsOfDateBackBy({ days: -2 });
            getDatePickerValue().then((value) => {
                selectedDate = value;
            });
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
            cy.get("div.collapse.show").should("be.visible");
            cy.clickViewDetails("Email Address", "View Details", "title");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading...");
            getDatePickerValue().then((newValue) => {
                expect(newValue).to.contain(selectedDate.slice(0, -3));
            });

            cy.navigateTopNav("Exposure");
            cy.get(".tab.nav.nav-tabs").find(".nav-link").contains("User").click().should("have.class", "active");
            selectCurrentTime();
            getDatePickerValue().then((value) => {
                const actualTimeNoSeconds = value.split(" ")[0];
                currentDate = actualTimeNoSeconds;
            });
            getDatePickerValue().as("currentTime");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
            cy.get("div.collapse.show").should("be.visible");
            cy.clickViewDetails("Email Address", "View Details", "title");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading...");
            getDatePickerValue().then((newValue) => {
                const newDate = newValue.split(" ")[0];
                expect(newDate).to.contain(currentDate);
            });
        }
    );

    it(
        "Admin can verify the entity view details page date selection functionality by selecting a past date and current date for device table",
        { tags: "regression" },
        () => {
            let selectedDate;
            let currentDate;
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Exposure");
            cy.get(".tab.nav.nav-tabs").find(".nav-link").contains("Device").click().should("have.class", "active");
            setAsOfDateBackBy({ days: -2 });
            getDatePickerValue().then((value) => {
                selectedDate = value;
            });
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
            cy.get("div.collapse.show").should("be.visible");
            cy.clickViewDetails("Hostname", "View Details", "title");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading...");
            getDatePickerValue().then((newValue) => {
                expect(newValue).to.contain(selectedDate.slice(0, -3));
            });
            cy.navigateTopNav("Exposure");
            cy.get(".tab.nav.nav-tabs").find(".nav-link").contains("Device").click().should("have.class", "active");
            selectCurrentTime();
            getDatePickerValue().then((value) => {
                const actualTimeNoSeconds = value.split(" ")[0];
                currentDate = actualTimeNoSeconds;
            });
            getDatePickerValue().as("currentTime");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
            cy.get("div.collapse.show").should("be.visible");
            cy.clickViewDetails("Hostname", "View Details", "title");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading...");
            getDatePickerValue().then((newValue) => {
                const newDate = newValue.split(" ")[0];
                expect(newDate).to.contain(currentDate);
            });
        }
    );
});
