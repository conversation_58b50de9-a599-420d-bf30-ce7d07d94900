import { clickSearch, setSearchParams, setSearchTable } from "../../iris/search/search_utils";

import {
    getValidSelectedOwnerAndClickDeviceDetails,
    verifyEntityLinksColumn,
    verifyEntityViewDetailsForDevice,
    verifyEntityViewDetailsForUser,
} from "./entity_utils";

describe("Tenant: Search -> Entity Detail", { tags: ["entity_view", "data_faker"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    const searchParams = {
        "Modified Time:": { Months: 4 },
    };

    it("Admin can verify the entity view details page for the device and user table", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
        const searchTable = ["Device", "User"];
        setSearchTable("Search For:", searchTable[0]);
        const filterParams = {
            "Modified Time:": { Months: 4 },
            "Selected Owner:": { IsNone: true },
            "Owner:": { IsNotNone: true },
        };
        setSearchParams(filterParams);
        clickSearch();
        cy.findByText("Loading Histogram...").should("not.exist");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
        const columnNames = [
            { name: "Hostname", links: "View Details" },
            {
                name: "IP Address",
                links: "View Details",
            },
        ];
        verifyEntityLinksColumn(columnNames);
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading...");
        verifyEntityViewDetailsForDevice();
        setSearchTable("Search For:", searchTable[1]);
        cy.findByText("Loading Histogram...").should("not.exist");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
        verifyEntityViewDetailsForUser();
    });

    it(
        "Administrator User: can download event history filename in jsonl format for Device",
        { tags: "regression" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Search");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
            setSearchTable("Search For:", "Device");

            cy.findByText("Loading Histogram...").should("not.exist");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");

            cy.clickViewDetails("Hostname", "View Details", "title");
            cy.findByRole("button", { name: "Now" }).click();
            cy.findByRole("button", { name: "Download Event History" })
                .click()
                .then(() => {
                    let titleName = null;
                    const formattedDate = new Date().toISOString().split("T")[0];
                    cy.get("h1.card-title").then(($el) => {
                        titleName = $el.contents().not("span").text().trim();
                        [titleName, formattedDate, ".jsonl"].forEach((fileCheck) => {
                            cy.verifyDownload(fileCheck, {
                                timeout: 100000,
                                interval: 600,
                                contains: true,
                            }).should("exist");
                        });
                    });
                });
        }
    );

    it("Admin user can view device updates from entity page", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
        cy.clickViewDetails("Hostname", "View Details", "title");
        let hostName;
        cy.get("h1.card-title")
            .invoke("text")
            .then((text) => {
                hostName = text.trim();
            });
        cy.findByRole("link", { name: "View Device Updates" })
            .should("be.visible")
            .invoke("removeAttr", "target")
            .click();
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
        cy.findByRole("table").find("tbody").find("tr").should("have.length.at.least", 1);
        cy.findByRole("table")
            .find("th")
            .should("exist")
            .filter(":contains(Hostname)")
            .invoke("index")
            .then((columnIndex) => {
                cy.findByRole("table")
                    .find("tbody")
                    .find("tr")
                    .each(($row) => {
                        cy.wrap($row)
                            .find("td")
                            .eq(columnIndex)
                            .find("div")
                            .eq(2)
                            .then(($text) => {
                                cy.wrap($text).invoke("text").should("equal", hostName);
                            });
                    });
            });
        cy.clickLogout();
    });

    it("Admin can verify the potential owners in the discovered owners table", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopNav("Search");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
        setSearchTable("Search For:", "Discovered Owners");
        const filterParams = {
            "Discovery Time:": { Months: 2 },
        };
        setSearchParams(filterParams);
        clickSearch();
        cy.clickViewDetails("Device", "View Details", "title");
        cy.findByText("Potential Owners")
            .should("exist")
            .then(($dt) => {
                const $dd = $dt.next("dd");
                cy.wrap($dd).invoke("text").should("exist").and("not.be.empty");
                cy.wrap($dd).findAllByRole("button", { name: "Select Owner" }).should("be.visible");
            });
    });

    it(
        "Admin User: Entity View Related Entities node graph play, pause, reset with update non-default filter settings without error",
        { tags: "regression" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Search");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
            setSearchTable("Search For:", "Device");
            cy.findByText("Add Parameter").closest(".react-select__control").click().type("Start Time");
            cy.findByRole("option", { name: "Start Time" }).click({
                force: true,
            });
            setSearchParams(searchParams);
            clickSearch();
            const columnNames = [
                { name: "Hostname", links: "View Details" },
                {
                    name: "IP Address",
                    links: "View Details",
                },
            ];
            verifyEntityLinksColumn(columnNames);
            cy.clickViewDetails("Hostname", "View Details", "title");
            cy.findByLabelText("Play/Pause").as("Play_Pause_Button");
            // Playing node graph visualization
            cy.get("@Play_Pause_Button").click();
            cy.findByLabelText("Reset").as("Reset_Button");
            cy.get("@Reset_Button").should("be.disabled");
            // Pause & Reset node graph visualization
            cy.get("@Play_Pause_Button").click();
            cy.get("@Reset_Button").should("be.enabled");
            cy.get("@Reset_Button").click();
            cy.get("#related-entities").find("button", { name: "Filter" }).first().click();
            cy.findByLabelText("Finding").parent().find(".react-switch-handle").click();
            cy.findByRole("button", { name: "Apply" }).click();
            cy.get("#related-entities")
                .should("exist")
                .within(() => {
                    cy.findByRole("alert").should("not.exist");
                });
            // Playing node graph visualization
            cy.get("@Play_Pause_Button").click();
            cy.findByLabelText("Reset").should("be.disabled");
            cy.get("#related-entities")
                .should("exist")
                .within(() => {
                    cy.findByRole("alert").should("not.exist");
                });
        }
    );

    it("Admin User: Verify Metadata Population and Findings Graph Content", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );

        cy.navigateTopNav("Security", "DataBee Findings");
        cy.retryTextNotContainCheck(".alert", "Loading DataBee Findings...");
        setSearchParams({ "User:": { IsNotNone: true } });
        clickSearch();
        cy.findByText("Loading Histogram...").should("not.exist");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
        cy.clickViewDetails("User: name", "View Details", "title");
        cy.get("canvas").should("exist");
        cy.get("body").then(($body) => {
            if ($body.find('*:contains(" is not active at this time. It was last seen on")').length) {
                cy.contains(" is not active at this time. It was last seen on").then(() => {
                    cy.get("p:has(button)").find("button").click();
                });
            }
        });
        cy.findByRole("button", { name: "View Full Details" })
            .click()
            .then(() => {
                cy.get("div.modal-content").within(() => {
                    cy.findAllByText("ldap_person").should("exist");
                });
            });
    });

    it(
        "Admin verifies that Selected Owner is Cleared and Confirmed Absent from Potential Owners List",
        { tags: "regression" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Search");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
            setSearchTable("Search For:", "Device");
            const filterParams = {
                "Modified Time:": { Months: 4 },
                "Selected Owner:": { IsNotNone: true },
            };
            setSearchParams(filterParams);
            clickSearch();
            cy.findByText("Loading Histogram...").should("not.exist");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
            getValidSelectedOwnerAndClickDeviceDetails();
            cy.findByText("Potential Owners").should("not.exist");
            cy.findByText("Selected Owner")
                .should("exist")
                .then(($dt) => {
                    const $dd = $dt.next("dd");
                    cy.wrap($dd).findByRole("button", { name: "Clear" }).click();
                });
            cy.findByText("device updated successfully.").should("be.visible");
            cy.findByText("Selected Owner").should("not.exist");
            cy.findByText("Potential Owners")
                .should("exist")
                .then(($dt) => {
                    const $dd = $dt.next("dd");
                    cy.wrap($dd)
                        .invoke("text")
                        .then((ownerText) => {
                            expect(ownerText).to.be.contains("-");
                        });
                    cy.wrap($dd).findByRole("button", { name: "Select Owner" }).should("not.exist");
                });
        }
    );

    let beforeTotalEvents;
    it(
        "Admin Verify Event Count Increase After Adding Events from Device Owners in Timeline",
        { tags: "regression" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.navigateTopNav("Search");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
            setSearchTable("Search For:", "Device");
            setSearchParams({
                "Modified Time:": { Months: 3 },
                "Owner:": { IsNotNone: true },
            });
            clickSearch();
            cy.findByText("Loading Histogram...").should("not.exist");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
            cy.clickViewDetails("Hostname", "View Details", "title");
            cy.findByText("Owner")
                .should("exist")
                .then(($dt) => {
                    const $dd = $dt.next("dd");
                    cy.wrap($dd).invoke("text").should("exist").and("not.be.empty");
                });
            cy.findByText(/out of \d+ total events/).then(($el) => {
                const text = $el.text();
                const totalEvents = text.match(/out of (\d+)/)[1];
                beforeTotalEvents = Number(totalEvents);
            });
            cy.intercept("GET", "**/api/entities/device/*/history/count/?end=**&message=&correlation=true").as(
                "getHistoryCount"
            );
            cy.findByLabelText("Include Events from Device Owner").check().should("be.checked");
            cy.wait("@getHistoryCount").then((interception) => {
                expect(interception.response.statusCode).to.equal(200);
            });
            cy.findByText("Loading...").should("not.exist");
            cy.findByRole("table").should("exist");
            cy.findByText(/out of \d+ total events/).then(($el) => {
                const text = $el.text();
                const totalEvents = text.match(/out of (\d+)/)[1];
                const afterTotalEvents = Number(totalEvents);
                expect(afterTotalEvents).to.be.a("number").and.greaterThan(beforeTotalEvents);
            });
        }
    );

    it(
        "Tenant Admin Verifies Potential Owners in Entity Details After Searching Device Table and Filtering Databee Findings by Message",
        { tags: ["regression", "demo_staging"] },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            cy.findByText("Search").click();
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading tables...");
            setSearchTable("Search For:", "Detection Finding");
            const searchMultiParams = {
                "Event Time:": { Months: 6 },
                "Message:": { In: ["HackTool - Rubeus Execution"] },
            };
            cy.findByText("Add Parameter").closest(".react-select__control").click().type("Message");
            cy.findByRole("option", { name: "Message" }).click({
                force: true,
            });
            setSearchParams(searchMultiParams);
            clickSearch();
            cy.findByText("Loading Histogram...").should("not.exist");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
            cy.clickViewDetails("Device: hostname", "View Details", "title");
            cy.findByRole("button", { name: /Now/i }).click();
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading...");
            cy.get("#related-entities").find("button", { name: "Filter" }).first().click();
            cy.findByLabelText("Finding").parent().find(".react-switch-handle").click();
            cy.findByRole("button", { name: "Apply" }).click();
            cy.get("#related-entities")
                .should("exist")
                .within(() => {
                    cy.get(".alert.alert-danger.show").should("not.exist");
                });
            let potentialOwnersText;
            cy.findByText("Potential Owners")
                .should("exist")
                .then(($dt) => {
                    const $dd = $dt.next("dd");
                    cy.wrap($dd)
                        .invoke("text")
                        .then((text) => {
                            potentialOwnersText = text;
                            cy.wrap($dd).contains(potentialOwnersText).should("exist").and("not.be.empty");
                        });
                });
            cy.navigateTopNav("Security", "DataBee Findings");
            cy.findByText("Add Parameter").closest(".react-select__control").click().type("Message");
            cy.findByRole("option", { name: "Message" }).click({
                force: true,
            });
            setSearchParams(searchMultiParams);
            clickSearch();
            cy.findByText("Loading Histogram...").should("not.exist");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Searching...");
            cy.clickViewDetails("Device: hostname", "View Details", "title");
            cy.get("#related-entities").find("button", { name: "Filter" }).first().click();
            cy.findByLabelText("Finding").parent().find(".react-switch-handle").click();
            cy.findByRole("button", { name: "Apply" }).click();
            cy.get("#related-entities")
                .should("exist")
                .within(() => {
                    cy.get(".alert.alert-danger.show").should("not.exist");
                });
            cy.findByText("Potential Owners")
                .should("exist")
                .then(($dt) => {
                    const $dd = $dt.next("dd");
                    cy.wrap($dd)
                        .invoke("text")
                        .then((text) => {
                            const potentialOwnersFindingsText = text;
                            expect(potentialOwnersText).to.equal(potentialOwnersFindingsText);
                        });
                });
        }
    );
});
