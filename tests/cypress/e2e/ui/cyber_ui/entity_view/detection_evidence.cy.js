/// <reference types="cypress"/>

describe("Tenant: Search -> Entity Detection Evidence", { tags: ["entity_view", "data_faker"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    it("Admin User can view supporting evidence for security finding", { tags: "regression" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.visit(`${Cypress.env("credentials").DATABEE_UI}entities/device/8`);
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading...");
        cy.findByRole("table").get("#event-history-table").find("tbody").should("be.visible");
        cy.findByRole("table")
            .find("tbody")
            .find("tr")
            .then(($elements) => {
                cy.wrap($elements.find("[title='Suppress Event']").first().closest("tr")).then(($element) => {
                    cy.wrap($element).as("suppressEvent");
                });
                cy.get("@suppressEvent").within(() => {
                    cy.get("div[class^='event-card-item']").as("eventCard");
                    cy.get("@eventCard").click();
                    cy.get("@eventCard").within(() => {
                        cy.findByText("Evidence Artifacts").should("be.visible");
                        cy.get("button[title='Open Backtrace Modal']").first().click({ force: true });
                    });
                });
            });
        cy.findByRole("dialog").as("eventDetailsDialog").should("be.visible");
        cy.get("@eventDetailsDialog").within(() => {
            cy.findByRole("heading", { level: 5 }).should("be.visible").should("contain.text", "Event Details");
            cy.get(".react-json-view").as("jsonObject").should("be.visible");
            cy.get("@jsonObject").within(() => {
                cy.get("span.object-key").then(() => {
                    cy.findByText("class_name").should("be.visible").parents(".variable-row").as("classNameRow");
                    cy.get("@classNameRow").within(() => {
                        cy.get(".variable-value")
                            .invoke("text")
                            .then((text) => {
                                cy.wrap(text.length).should("be.greaterThan", 1);
                            });
                    });
                });
            });
        });
    });
});
