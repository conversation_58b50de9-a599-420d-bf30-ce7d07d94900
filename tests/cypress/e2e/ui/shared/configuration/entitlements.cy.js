/// <reference types="cypress"/>

describe(
    "Shared: Entitlements configuration for both cluster and DataBee ",
    { tags: ["realCluster", "entitlements", "full"] },
    () => {
        afterEach(() => {
            cy.clickLogout();
        });

        beforeEach(() => {
            cy.clearCookies();
        });

        const tenantName = Cypress.env("credentials").DATABEE_UI_NAME;
        const entitlementDefaults = Cypress.env("ENTITLEMENTS");

        function updateEntitlements(entitlements, checkOptions = false) {
            cy.visit(Cypress.env("credentials").CLUSTER_UI);
            cy.manualLogin(
                Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
                Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
            );
            cy.typeInHeaderInput("Name", tenantName);
            cy.clickViewDetails("Actions", "Details");
            if (checkOptions) {
                entitlements.forEach((entitle) => {
                    cy.updateForm({ [entitle]: undefined });
                });
            } else {
                entitlements.forEach((entitle) => {
                    cy.updateForm({ [entitle]: "uncheck" });
                });
            }
            cy.findByRole("button", { name: "Submit" }).click();
            cy.findByText("Tenant details updated.").should("exist");
        }

        it(
            "Admin user can verify that security and entity resolution menus should not be visible on the tenant page",
            { tags: "regression" },
            () => {
                updateEntitlements(entitlementDefaults);
                cy.visit(Cypress.env("credentials").DATABEE_UI);
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                    Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
                );
                cy.findByText("Security").should("not.exist");
                cy.navigateTopRightNav("Configuration", "System");
                cy.findByText("BeeKeeper").should("not.exist");
                cy.findByText("Security").should("not.exist");
            }
        );

        it(
            "Admin user can verify that security and entity resolution menus should be visible on the tenant page",
            { tags: "smoke" },
            () => {
                updateEntitlements(entitlementDefaults, true);
                cy.visit(Cypress.env("credentials").DATABEE_UI);
                cy.manualLogin(
                    Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                    Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
                );
                cy.findAllByText("Security")
                    .parent()
                    .first()
                    .within(($el) => {
                        cy.wrap($el).click();
                        cy.findByText("DataBee Findings").should("be.visible");
                        cy.findByText("Detection Streams").should("be.visible");
                        cy.findByText("Detection Chains").should("be.visible");
                        cy.findByText("Suppress List").should("be.visible");
                    });

                cy.navigateTopRightNav("Configuration", "System");
                cy.findByText("BeeKeeper").should("be.visible");
                cy.navigateTopRightNav("Configuration", "Security");
                cy.findByRole("link", { name: "Active Detections" }).should("be.visible");
                cy.findByText("Rules Management").should("be.visible");
            }
        );
    }
);
