import { navigateTenantDetails } from "../../cluster/tenant/tenant_utils";

import { purchaseDashboard, removePurchasedDashboard, setDeliveryType } from "./content_delivery_utils";

describe(
    "Embedded Tableau: Dashboard Management",
    { tags: ["smoke", "realCluster", "full", "content_delivery"] },
    () => {
        afterEach(() => {
            cy.clickLogout();
        });

        it("should allow cluster admin to assign reports and compliance user to view them correctly", () => {
            const reportNames = ["Endpoint Detection Response", "Executive KPI"];

            // Step 1: Login as Cluster Admin and assign reports
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").CLUSTER_UI);
            cy.manualLogin(
                Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
                Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
            );

            const tenantName = Cypress.env("credentials").DATABEE_UI_NAME;
            navigateTenantDetails(tenantName);

            // Remove previously purchased dashboards
            reportNames.forEach((report) => {
                removePurchasedDashboard(report);
                cy.get(".react-select__input").should("not.contain.text", report);
            });

            // Set delivery type and purchase new dashboards
            cy.reload();
            setDeliveryType("Embedded");

            // purchase dashboards
            reportNames.forEach((report) => {
                purchaseDashboard(report);
                cy.get(".react-select__multi-value__label").should("contain.text", report);
            });

            // Step 2: Login as Compliance User and verify reports are visible
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").DATABEE_UI);
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_COMPLIANCE_ROLE.USERNAME,
                Cypress.env("credentials").DATABEE_COMPLIANCE_ROLE.PASSWORD
            );

            // Ensure restricted menu items are not visible for compliance role
            cy.findByText("Console").should("not.exist");
            cy.findByText("Data").should("not.exist");
            cy.findByText("Security").should("not.exist");
            cy.findByText("Entities").should("not.exist");
            cy.findByText("Search").should("not.exist");
            cy.findByTitle("Configuration").should("not.exist");

            // Navigate to Reports page
            cy.navigateTopNav("Compliance", "Reports");
            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading CCM Report List...");

            // Filter by Purchased Reports
            cy.findByLabelText("Purchase Status:").should("be.visible");
            cy.findByText("Purchased", { selector: ".react-select__single-value" }).should("be.visible");
            cy.findByLabelText("Search").should("be.visible");

            // Verify each report is visible and navigable
            reportNames.forEach((report) => {
                if (report === "Executive KPI") {
                    cy.navigateTopNav("Compliance", "CCM Executive Summary");
                } else {
                    cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading CCM Report List...");
                    cy.findByPlaceholderText("search by report name").click({ force: true }).clear().type(report);
                    cy.findAllByText(report).should("be.visible");
                    cy.findByText("View Report").click();
                }

                // Verify report header and return to list
                cy.findByText(report, { timeout: 10000 }).should("be.visible");
                cy.intercept("POST", "https://us-east-1.online.tableau.com/**/startSession/**").as("tableauSession");
                cy.wait("@tableauSession").its("response.statusCode").should("eq", 200);
                cy.findByRole("link", { name: "Reports" }).should("be.visible").click();
            });
        });
    }
);
