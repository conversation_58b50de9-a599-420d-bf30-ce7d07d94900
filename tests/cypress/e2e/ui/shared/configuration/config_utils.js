const createSsoConnection = (uploadFileName, SSOName, SSOButtonName, SSOAdministratorGroup, setDefault) => {
    cy.fixture(uploadFileName).as("uploadFile");
    cy.findByText("Add Connection").click();
    cy.findByLabelText("Name *").type(SSOName);
    cy.findByLabelText("Button Text *").type(SSOButtonName);
    if (setDefault) {
        cy.findByLabelText("Cluster Administrator Group").type(`${SSOAdministratorGroup}{enter}`);
    } else {
        cy.findByLabelText("Administrator Group").type(`${SSOAdministratorGroup}{enter}`);
    }
    cy.findByLabelText("Attach File").selectFile("@uploadFile", { force: true });
    cy.findByText("Establish Connection").click();
    cy.findByText("Configuration updated.", { timeout: 70000 }).should("be.visible");
};

const extractTextFromUrl = (url) => {
    const regex = /^https?:\/\/([^.]+)\./;
    const match = url.match(regex);
    return match ? match[1] : "";
};

const deleteRulesConfig = (reposName) => {
    cy.contains("p", reposName).click();
    cy.findByRole("button", { name: "Delete" }).click();
    cy.findAllByRole("dialog")
        .last()
        .findByRole("group")
        .within(() => {
            cy.findByText("Close").should("be.enabled");
            cy.findByText("Delete").click();
        });
    cy.contains("p", reposName).should("not.exist");
};

const deleteAllRulesConfig = () => {
    const baseUrl = Cypress.env("credentials").DATABEE_UI;
    const username = Cypress.env("credentials").DATABEE_USER_IDS.ADMIN;
    const password = Cypress.env("credentials").DATABEE_ADMIN_PASSWORD;

    cy.getAuthToken(baseUrl, username, password).then((token) => {
        // Step 1: Fetch all rule IDs
        cy.request({
            method: "GET",
            url: `${baseUrl}api/config/security/rules_management/`,
            headers: {
                Authorization: `Bearer ${token}`,
            },
        }).then((getResponse) => {
            expect(getResponse.status).to.eq(200);

            const ruleIds = getResponse.body.items.map((item) => item.id);
            cy.log(`Found ${ruleIds.length} rules`);

            // Step 2: Delete each rule
            ruleIds.forEach((id) => {
                cy.request({
                    method: "DELETE",
                    url: `${baseUrl}api/config/security/rules_management/${id}/`,
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                }).then((deleteResponse) => {
                    expect(deleteResponse.status).to.eq(200);
                    expect(deleteResponse.body.detail).to.eq("Successfully deleted rules management configuration");
                    cy.log(`Deleted Rule ID: ${id}`);
                });
            });
        });
    });
};

const deleteAllDataCollectorConfig = () => {
    const baseUrl = Cypress.env("credentials").DATABEE_UI;
    const username = Cypress.env("credentials").DATABEE_USER_IDS.ADMIN;
    const password = Cypress.env("credentials").DATABEE_ADMIN_PASSWORD;

    cy.getAuthToken(baseUrl, username, password).then((token) => {
        // Step 1: Fetch all data collector IDs
        cy.request({
            method: "GET",
            url: `${baseUrl}api/config/system/data_collectors/`,
            headers: {
                Authorization: `Bearer ${token}`,
            },
        }).then((getResponse) => {
            expect(getResponse.status).to.eq(200);

            const dataCollectorIds = getResponse.body.items.map((item) => item.id);
            cy.log(`Found ${dataCollectorIds.length} data collectors`);

            // Step 2: Delete each data collector config
            dataCollectorIds.forEach((id) => {
                cy.request({
                    method: "DELETE",
                    url: `${baseUrl}api/config/system/data_collectors/${id}/`,
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                }).then((deleteResponse) => {
                    expect(deleteResponse.status).to.eq(200);
                    expect(deleteResponse.body.detail).to.eq("Successfully deleted data collectors configuration");
                    cy.log(`Deleted DataCollector ID: ${id}`);
                });
            });
        });
    });
};

export {
    createSsoConnection,
    deleteAllDataCollectorConfig,
    deleteAllRulesConfig,
    deleteRulesConfig,
    extractTextFromUrl,
};
