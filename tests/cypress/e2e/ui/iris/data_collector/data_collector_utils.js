/// <reference types="cypress"/>
// Methods
const fillBasicInfo = (update) => {
    const randomString = Math.random().toString(36).substring(8);
    const randomNumber = Math.floor(Math.random() * (1000 - 100 + 1)) + 100;
    const dcName = `dataCollector${randomString}-${randomNumber}`;
    const proxyUrl = "http://localhost.com";
    const databeeUser = "databee_user";
    const databeePwd = "databee_pwd";

    cy.findByLabelText("Collector Name *").clear().type(dcName);
    cy.findByLabelText("OS *").should("exist");
    cy.findByLabelText("Enable Proxy").check({
        force: true,
    });
    if (update) {
        cy.findByLabelText("Proxy URL *").clear().type(proxyUrl);
        cy.findByLabelText("Proxy Username *").clear().type(databeeUser);
        cy.findByLabelText("Password *").clear().type(databeePwd);
    } else {
        cy.findByLabelText("Proxy URL *").type(proxyUrl);
        cy.findByLabelText("Proxy Username *").type(databeeUser);
        cy.findByLabelText("Password *").type(databeePwd);
    }
    return dcName;
};

const verifyInstallationSteps = () => {
    cy.findByText("Tenant ID").should("exist");
    cy.findByLabelText("Receiver URL").should("exist");
    cy.findByText("Collector ID").should("exist");
    cy.findByLabelText("API Key").should("exist");
    cy.contains("h4", "Installation Steps")
        .parent()
        .scrollIntoView()
        .within(() => {
            cy.get("input").invoke("val").should("exist");
        });
    cy.findByText("API Key")
        .parent()
        .scrollIntoView()
        .within(() => {
            cy.get("input").invoke("val").should("exist");
            cy.findByRole("button", { name: "Copy to clipboard" }).should("be.visible");
            cy.findByRole("button", { name: "Show API Key" }).should("be.visible");
            cy.findByRole("button", { name: "Show API Key" }).click();
            cy.findByRole("button", { name: "Hide API Key" }).should("be.visible");
        });
    cy.findByText("Tenant ID")
        .parent()
        .scrollIntoView()
        .within(() => {
            cy.get("input").invoke("val").should("exist");
            cy.findByRole("button", { name: "Copy to clipboard" }).should("be.visible");
        });

    cy.findByText("Receiver URL")
        .parent()
        .scrollIntoView()
        .within(() => {
            cy.get("input").invoke("val").should("exist");
            cy.findByRole("button", { name: "Copy to clipboard" }).should("be.visible");
        });

    cy.findByText("Collector ID")
        .parent()
        .scrollIntoView()
        .within(() => {
            cy.get("input").invoke("val").should("exist");
            cy.findByRole("button", { name: "Copy to clipboard" }).should("be.visible");
        });
};

const verifyDataSourceSection = () => {
    cy.get("a.nav-link").filter(':contains("Data Feeds")').click();
    cy.findByText("This collector is not associated with any data feeds").should("be.visible");
};

const getUserEmail = () => {
    cy.findByTitle("My Profile").click();
    cy.findByRole("menuitem", {
        name: "My Profile",
    }).click();
    cy.get('input[class="form-control"]')
        .eq(3)
        .invoke("val")
        .then((text) => {
            const getEmail = text;
            cy.log(getEmail);
            cy.wrap(getEmail).as("email");
        });

    cy.findByTitle("Configuration")
        .parent()
        .within(($el) => {
            cy.wrap($el).click();
            cy.findByText("System").click();
        });

    cy.findByText("Data Collectors").click();
};

const verifyCardDetails = (dataCollectorName) => {
    getUserEmail();
    cy.findByText(dataCollectorName).should("be.visible");
    cy.get('[id="system-data_collectors-list"]')
        .find("div")
        .first()
        .within(() => {
            cy.get("@email").then((email) => {
                cy.findByText(email).should("be.visible");
            });
            cy.findByText("Data Feeds")
                .parent()
                .within(() => {
                    cy.get("td").invoke("text").should("be.exist");
                });
            cy.findByText("Created On")
                .parent()
                .within(() => {
                    cy.get("td").invoke("text").and("not.be.empty");
                });
        });
};

/**
 * Copies installation instructions and stores them in a file for use by pytest test cases in the workflow.
 */
const copyInstallInstructions = (collectorName) => {
    const installConfig = {};
    cy.get('[id$="install_script"]')
        .invoke("val")
        .then((value) => {
            installConfig.script = value;
        });

    const params = ["Tenant ID", "Receiver URL", "Collector ID", "API Key"];
    params.forEach((param) => {
        const jsonKey = param.toLowerCase().replace(/\s/g, "_");
        cy.findByText(param)
            .parent()
            .scrollIntoView()
            .within(() => {
                cy.get("input")
                    .invoke("val")
                    .then((value) => {
                        installConfig[jsonKey] = value;
                    });
            });
    });
    const filePath = `cypress/context/${collectorName}.json`;
    cy.writeFile(filePath, installConfig);
};

const deleteDataCollector = (collectorName) => {
    cy.findAllByText(collectorName)
        .parent()
        .first()
        .within(($el) => {
            cy.wrap($el).click();
        });
    cy.findByRole("button", { name: "Delete" }).should("exist").click();
    cy.findAllByText("Delete").eq(1).click();
    cy.findByText(collectorName).should("not.exist");
};

const gotoDataCollector = () => {
    cy.navigateTopRightNav("Configuration", "System");
    cy.findByRole("link", { name: "Data Collectors" }).click();
};
const gotoAddNewDataCollector = () => {
    gotoDataCollector();
    cy.findByRole("button", { name: "Add Data Collector" }).click();
};

export {
    copyInstallInstructions,
    deleteDataCollector,
    fillBasicInfo,
    gotoAddNewDataCollector,
    gotoDataCollector,
    verifyCardDetails,
    verifyDataSourceSection,
    verifyInstallationSteps,
};
