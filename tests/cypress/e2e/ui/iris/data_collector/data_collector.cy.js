/// <reference types="cypress"/>
import {
    copyInstallInstructions,
    deleteDataCollector,
    fillBasicInfo,
    gotoAddNewDataCollector,
    verifyCardDetails,
    verifyDataSourceSection,
    verifyInstallationSteps,
} from "./data_collector_utils";

describe("Tenant: Data collector Configuration", { tags: ["full", "data-collector"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    });

    afterEach(() => {
        cy.clickLogout();
    });

    let defaultCollectorName;

    it("If the Proxy is enabled it shouldn't go next without url,username and password", { tags: "smoke" }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopRightNav("Configuration", "System");
        cy.findByText("Data Collectors").click();
        cy.findByRole("button", { name: "Add Data Collector" }).click();
        fillBasicInfo(false);
        cy.findByLabelText("Proxy URL *").clear();
        cy.findByText("Required!").should("be.visible");
        cy.findByLabelText("Close").click();
    });

    it(
        "User can create a new data collector with the widget and can disable,enable,update and delete",
        { tags: "smoke" },
        () => {
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );

            // create
            gotoAddNewDataCollector();
            cy.findAllByText("Next").should("be.disabled");
            cy.contains("a", "Installation Steps").click({ force: true });
            cy.findByLabelText("API Key").should("not.exist");
            // Verify the Proxy fields disabled.
            cy.findByLabelText("Enable Proxy").should("not.be.checked");
            cy.findByLabelText("Proxy URL").should("be.disabled");
            cy.findByLabelText("Proxy Username").should("be.disabled");
            cy.findByLabelText("Password").should("be.disabled");
            const dataCollectorName = fillBasicInfo(false);
            cy.get(".modal-body")
                .parent()
                .within(() => {
                    cy.findByText("Next").click();
                });
            cy.findByText("Configuration created successfully").should("be.exist");
            verifyInstallationSteps();
            cy.get(".modal-body")
                .parent()
                .within(() => {
                    cy.findByText("Close").click();
                });
            verifyCardDetails(dataCollectorName);

            // Disable
            cy.findByText(dataCollectorName).click();
            verifyDataSourceSection();
            cy.findByRole("button", { name: "Disable" }).click();
            cy.findAllByText("Disable").eq(1).click();
            cy.findByText("Configuration updated successfully.").should("be.visible");

            // Enable
            cy.findByText(dataCollectorName).click();
            cy.findByRole("button", { name: "Enable" }).click();
            cy.findAllByText("Enable").eq(1).click();
            cy.findByText("Configuration updated successfully.").should("be.visible");

            // Update
            cy.findByText(dataCollectorName).click();
            // verify OS* should be non-editable
            cy.findByLabelText("OS *").should("be.disabled");
            const newDataCollectorName = fillBasicInfo(true);
            cy.findByRole("button", { name: "Update" }).click();
            cy.findByText("Configuration updated successfully.").should("be.visible");
            cy.get(".modal-body")
                .parent()
                .within(() => {
                    cy.findByText("Close").click();
                });
            cy.findByText(dataCollectorName).should("not.exist");
            cy.findByText(newDataCollectorName).should("exist");

            // Finally delete
            deleteDataCollector(newDataCollectorName);
        }
    );

    it("Configure Collectors Without proxy", { tags: ["e2e_setup"] }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopRightNav("Configuration", "System");
        cy.findByRole("link", { name: "Data Collectors" }).click();
        const randomString = Math.random().toString(36).substring(8);
        defaultCollectorName = `data-collector-${randomString}`;
        const dataCollectorName = Cypress.env("collectorName") ?? defaultCollectorName;
        cy.findByRole("button", { name: "Add Data Collector" }).click();
        cy.updateForm({
            "Collector Name *": dataCollectorName,
            "OS *": Cypress.env("os") ?? "Linux",
        });
        cy.get("#configure-collector-btn").click();
        verifyInstallationSteps();
        copyInstallInstructions(dataCollectorName);
        cy.findByLabelText("Close", { selector: "button" }).click();
    });

    it("Disable/Enable Collector State", { tags: ["e2e_tests"] }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        cy.navigateTopRightNav("Configuration", "System");
        cy.findByRole("link", { name: "Data Collectors" }).click();
        const dataCollectorName = Cypress.env("collectorName") ?? defaultCollectorName;
        cy.findAllByText(dataCollectorName)
            .parent()
            .first()
            .within(($el) => {
                cy.wrap($el).click();
            });
        cy.findByRole("button", { name: "Disable" }).click();
        cy.findAllByText("Disable").eq(1).click();
        cy.findByText("Configuration updated successfully.").should("be.visible");
        cy.get("i.fa-solid.fa-circle-xmark").should("be.visible");

        cy.findAllByText(dataCollectorName)
            .parent()
            .first()
            .within(($el) => {
                cy.wrap($el).click();
            });
        cy.findByText(`${dataCollectorName} is disabled. To re-enable click on the enable button below.`).should(
            "be.visible"
        );
        cy.findByRole("button", { name: "Enable" }).click();
        cy.findAllByText("Enable").eq(1).click();
        cy.findByText("Configuration updated successfully.").should("be.visible");
        cy.get("i.fa-solid.fa-circle-check").should("be.visible");
    });

    it("Remove the Collector Created", { tags: ["e2e_cleanup"] }, () => {
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        const collectorName = Cypress.env("collectorName") ?? defaultCollectorName;
        cy.navigateTopRightNav("Configuration", "System");
        cy.findByRole("link", { name: "Data Collectors" }).click();
        cy.findAllByText(collectorName)
            .parent()
            .first()
            .within(($el) => {
                cy.wrap($el).click();
            });
        cy.findByRole("button", { name: /delete/i }).click();
        cy.findAllByText("Delete").eq(1).click();
        cy.findByText(collectorName).should("not.exist");
    });
});
