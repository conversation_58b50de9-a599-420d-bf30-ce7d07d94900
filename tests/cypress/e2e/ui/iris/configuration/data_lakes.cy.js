/// <reference types="cypress"/>

import { accessConfig, DATAL<PERSON>KE, fillDataLakeForm, testConnection } from "./config_utils";

const testType = Cypress.env("TEST_TYPE");

const commonHooks = {
    beforeEach: () => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
    },
    afterEach: () => {
        cy.clickLogout();
    },
};

const datalakeConfig = DATALAKE.SNOWFLAKE;
after(() => {
    cy.clearCookies();
    cy.visit(Cypress.env("credentials").DATABEE_UI);
    cy.manualLogin(
        Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
        Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
    );
    cy.navigateTopRightNav("Configuration", "System");
    cy.findByText(/Select your preferred data lake for data output/).should("be.visible");
    cy.get("body").then(($body) => {
        if (!$body.find(".fa-circle-check").length) {
            accessConfig();
            testConnection(datalakeConfig, "submitConfig");
            cy.findByLabelText("Close").click();
            cy.contains("Configuration updated successfully.").should("be.visible");
        }
    });
    cy.clickLogout();
});

switch (testType) {
    case "SnowFlake":
    case "DataFaker":
        describe(
            `Tenant: Configuration -> System -> Data Lakes (${testType} Tests)`,
            { tags: ["smoke", "realCluster", "config", "full", "data_lake", "snowflake"] },
            () => {
                beforeEach(commonHooks.beforeEach);
                afterEach(commonHooks.afterEach);

                it(
                    "Admin can validate, test, configure, and disable, enable Snowflake connection",
                    { tags: ["smoke", "regression"] },
                    () => {
                        cy.manualLogin(
                            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
                        );
                        accessConfig();
                        cy.findByAltText(DATALAKE.SNOWFLAKE).click();

                        // Validation Checks for required fields
                        fillDataLakeForm(DATALAKE.SNOWFLAKE);

                        const fields = [
                            {
                                label: "Account Identifier *",
                                value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.ACCOUNT_IDENTIFIER,
                            },
                            { label: "Username *", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.USERNAME },
                            { label: "Database *", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.DATABASE },
                            { label: "Warehouse *", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.WAREHOUSE },
                            { label: "Role *", value: Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.ROLE },
                        ];

                        fields.forEach(({ label, value }) => {
                            cy.findAllByLabelText(label).click().clear();
                            cy.findByText("Required!").should("be.visible");
                            cy.findAllByLabelText(label).click().type(value);
                            cy.findByText("Required!").should("not.exist");
                        });

                        // Private Key Field
                        cy.findAllByLabelText("Private Key *").should("not.be.empty").click();
                        cy.findAllByLabelText("Private Key *").should("have.value", "");
                        cy.findByText("Required!").should("be.visible");
                        cy.findAllByLabelText("Private Key *")
                            .click()
                            .type(Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.PRIVATE_KEY);
                        cy.findByText("Required!").should("not.exist");

                        // Private Key Password
                        cy.findAllByLabelText("Private Key Password").invoke("val").should("not.be.empty");
                        cy.findAllByLabelText("Private Key Password")
                            .click()
                            .type(Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.PASSWORD);

                        // Test Connectivity
                        cy.findByRole("button", { name: "Test Connectivity" }).click();
                        cy.contains("Connection to Snowflake successful", { timeout: 60000 }).should("be.exist");

                        // Submit Configuration
                        cy.setInput("Enabled *");
                        cy.findByRole("button", { name: "Submit" }).click();
                        cy.contains("Configuration updated successfully.", { timeout: 60000 }).should("be.exist");

                        // Disable Configuration
                        cy.findByAltText(DATALAKE.SNOWFLAKE).click();
                        cy.setInput("Enabled *", "uncheck");
                        cy.findByRole("button", { name: "Submit" }).click();
                        cy.contains("Configuration updated successfully.", { timeout: 60000 }).should("be.exist");

                        // Enable Configuration
                        cy.findByAltText(DATALAKE.SNOWFLAKE).click();
                        cy.setInput("Enabled *");
                        cy.findByRole("button", { name: "Submit" }).click();
                        cy.contains("Configuration updated successfully.", { timeout: 60000 }).should("be.exist");
                    }
                );
            }
        );
        break;

    default:
        break;
}
