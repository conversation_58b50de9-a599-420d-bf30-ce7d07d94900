/// <reference types="cypress"/>

import { navigateTenantDetails } from "../../cluster/tenant/tenant_utils";
import {
    cancelDashboardRequest,
    goToContentDelivery,
    purchaseDashboard,
    removePurchasedDashboard,
    requestDashboard,
    USER_ROLES,
} from "../../shared/configuration/content_delivery_utils";

describe("Tenant: Content Delivery", { tags: ["full", "content_delivery"] }, () => {
    before(() => {
        cy.visit(Cypress.env("credentials").CLUSTER_UI);
        cy.manualLogin(
            Cypress.env("credentials").CLUSTER_USER_IDS.ADMIN,
            Cypress.env("credentials").CLUSTER_ADMIN_PASSWORD
        );

        const tenantName = Cypress.env("credentials").DATABEE_UI.split("//")[1].split(".")[0];
        navigateTenantDetails(tenantName);

        // Check if the Application Security dashboard is already purchased and remove if so
        removePurchasedDashboard("Application Security");

        // Check if the Application Security dashboard has been requested and cancel if so
        cancelDashboardRequest("Application Security");

        // Ensure that Asset Management is always purchased
        purchaseDashboard("Asset Management");

        cy.clickLogout();
    });

    it("should allow admin to purchase dashboard", () => {
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(USER_ROLES.admin.username, USER_ROLES.admin.password);

        goToContentDelivery();

        requestDashboard("Application Security", true);

        cy.clickLogout();
    });
});

Object.keys(USER_ROLES).forEach((role) => {
    describe(`Tenant: Content Delivery | Role: ${role}`, { testIsolation: false }, () => {
        before(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").DATABEE_UI);
            cy.manualLogin(USER_ROLES[role].username, USER_ROLES[role].password);
            goToContentDelivery();
        });

        after(() => {
            cy.clickLogout();
        });

        it("should display the correct dashboards", () => {
            cy.findByText("Application Security").should("be.visible");
            cy.findByText("Asset Management").should("be.visible");
            cy.findByText("Endpoint Detection Response").should("be.visible");
        });

        it("should allow searching for 'Phishing'", () => {
            cy.findAllByLabelText("Search").click().type("Phishing");

            // Phishing dashboard should be the only result
            cy.findByText("Phishing").should("be.visible");
            cy.findByText("1 result").should("be.visible");
            cy.findByText("14 available").should("be.visible");

            cy.findByText("Application Security").should("not.exist");
            cy.findByText("Asset Management").should("not.exist");
            cy.findByText("Endpoint Detection Response").should("not.exist");

            // Clear the search
            cy.findAllByLabelText("Search").clear();
        });

        it("should allow filtering by Purchase Status", () => {
            // Filter by purchase status
            cy.contains("label", "Purchase Status")
                .parent()
                .find(".react-select__control")
                .click()
                .get(".react-select__menu")
                .contains(".react-select__option", "Purchased")
                .click();

            cy.findByText("Purchased").should("be.visible");
            cy.findByText("Application Security").should("not.exist");

            // Reset the filter
            cy.contains("label", "Purchase Status")
                .parent()
                .find(".react-select__control")
                .click()
                .get(".react-select__menu")
                .contains(".react-select__option", "All")
                .click();
        });

        it("should allow filtering by BI Tool", () => {
            // Filter by BI Tool
            cy.contains("label", "BI Tool")
                .parent()
                .find(".react-select__control")
                .click()
                .get(".react-select__menu")
                .contains(".react-select__option", "PowerBI")
                .click();

            cy.findByText("PowerBI").should("be.visible");
            cy.findByText("14 results").should("be.visible");
            cy.findByText("14 available").should("be.visible");
            cy.findByText("Application Security").should("be.visible");
        });

        it("should allow downloading the 'Asset Management' dashboard", () => {
            // Download dashboard
            cy.findByText("Asset Management").should("be.visible");

            cy.contains(".card", "Asset Management").within(() => {
                cy.findByRole("button", { name: "Download" }).click();
            });

            // Wait for the download to complete
            cy.get(".fas.fa-spinner.fa-pulse").should("exist");
            cy.get(".fas.fa-spinner.fa-pulse").should("not.exist");

            cy.contains("commercial/asset_management/CCM 3.0 Asset Management.pbit downloaded successfully").should(
                "be.visible"
            );
        });

        it("should confirm that the Tableau variant works as well", () => {
            // Confirm that the Tableau variant works as well
            cy.contains("label", "BI Tool")
                .parent()
                .find(".react-select__control")
                .click()
                .get(".react-select__menu")
                .contains(".react-select__option", "Tableau")
                .click();

            cy.findByText("Asset Management").should("be.visible");

            cy.contains(".card", "Asset Management").within(() => {
                cy.findByRole("button", { name: "Download" }).click();
            });

            // Wait for the download to complete
            cy.get(".fas.fa-spinner.fa-pulse").should("exist");
            cy.get(".fas.fa-spinner.fa-pulse").should("not.exist");

            cy.contains("commercial/asset_management/CCM 3.0 Asset Management.twbx downloaded successfully").should(
                "be.visible"
            );
        });

        it("should allow syncing the 'Asset Management' dashboard", () => {
            // Sync dashboard
            cy.findByText("Asset Management").should("be.visible");

            cy.contains(".card", "Asset Management").within(() => {
                cy.findByRole("button", { name: "Deploy" }).click();
            });

            cy.findByRole("button", { name: "Yes" }).click();

            // Wait for the sync to complete
            cy.get(".fas.fa-spinner.fa-pulse").should("exist");
            cy.get(".fas.fa-spinner.fa-pulse").should("not.exist");

            cy.contains("Content synced").should("be.visible");
        });
    });
});
