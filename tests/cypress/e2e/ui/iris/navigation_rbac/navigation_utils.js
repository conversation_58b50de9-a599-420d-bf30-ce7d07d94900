// css selectors
import { extractTextFromUrl } from "../../shared/configuration/config_utils";

const dashboard = "div#dashboard";
const navigationSubMenu = "div.container-fluid div.ml-auto.sub-menu";

// Methods
export function verifyDashboard() {
    cy.get(dashboard).within(() => {
        cy.findByText("Overview").should("be.visible");
        cy.findByText("Feed Health", { exact: false }).should("be.visible");
        cy.findByText("Manage Console").should("be.visible");
        cy.findByText("Manage Console").click();
        cy.findByText("Create Console").should("be.visible");
        cy.findByText("Manage Console").click();
    });
}

export function overlayDashboard() {
    // validate Overlay Create Console
    cy.findByText("Manage Console").click();
    cy.findByText("Create Console").click();
    cy.findAllByText("Create Console").last().should("be.visible");
    cy.findByText("Create").should("be.visible");
    cy.findByText("Close").click();
}

export function configNotExist() {
    cy.get(navigationSubMenu).should("be.visible").and("exist");
    cy.findByTitle("Configuration").should("not.exist");
}

export function accessSystemtab() {
    cy.navigateTopRightNav("Configuration", "System");
    cy.findByAltText("Snowflake").click();
    cy.findByRole("button", { name: "Test Connectivity" }).scrollIntoView();
    cy.findByRole("button", { name: "Test Connectivity" }).should("be.visible").and("be.enabled");
    cy.findByRole("button", { name: "Submit" }).should("be.visible").and("be.disabled");
    cy.findByRole("button", { name: "Close" }).click();
    /// /Validate Retention page
    cy.navigateTopRightNav("Configuration", "System", "Retention");
    cy.findByText("Retention Policies").should("be.visible");
}

export function accessManagementTab() {
    cy.navigateTopRightNav("Configuration", "Access Management");
    cy.contains("h2", "Single Sign-On (SSO)").should("be.visible");
    /// / Validate users Section
    cy.navigateTopRightNav("Configuration", "Access Management", "Users");
    cy.contains("h2", "Users").should("be.visible");
    /// / Session Management Section
    cy.navigateTopRightNav("Configuration", "Access Management", "Session Management");
    cy.contains("h2", "Session Management").should("be.visible");
    cy.findByRole("button", { name: "Submit" }).should("be.visible").and("be.disabled");
}

export function accessUsers() {
    cy.navigateTopRightNav("Configuration", "Access Management", "Users");
    cy.contains("h2", "Users").should("be.visible");
}

// Data tab access
export function goToDataTab() {
    cy.navigateTopNav("Data", "Data Feeds");
    cy.findByText("Your current data feeds").should("be.visible").and("exist");
}
export function gotoDataSource() {
    goToDataTab();
    cy.findByRole("button", { name: "Add New Data Feed" }).click();
    cy.findByText("Add new data feed").should("be.visible").and("exist");
    cy.findByRole("button", { name: "Back" }).click();
}
export function noAccessToDataSource() {
    goToDataTab();
    cy.findByText("Add New Data Feed").should("not.exist");
}

// Functions related to Search page
export function gotoSearch() {
    cy.navigateTopNav("Search");
    cy.findByText("Search History").should("be.visible").and("exist");
    cy.findByText("Saved Searches").should("be.visible").and("exist");
}

// Function related to help links
export function goToHelpLinks() {
    cy.navigateTopRightNav("Documentation", "API Documentation");
    cy.contains("a", "Swagger API page").should("be.visible");
    cy.findByTitle("Documentation").click();
    cy.findByText("Online User Manual").click();
    cy.request("https://docs.databee.buzz/docs").then((response) => {
        expect(response.status).to.eq(200);
    });
}

// Functions related to notification
export function accessNotifications() {
    cy.navigateTopRightNav("Notifications");
    cy.get("a.btn.btn-secondary.btn-sm").should("have.text", "See All").click();
    cy.get("label.btn.btn-secondary.btn-sm").first().should("have.text", " All").and("be.visible");
    cy.get("label.btn.btn-secondary.btn-sm").last().should("have.text", " Unread").and("be.visible");
    cy.get(".ml-5 input.form-control").should("be.visible").type("ABC").and("have.value", "ABC");
}

// functions related to my profile
export function accessMyProfile() {
    cy.navigateTopRightNav("My Profile");
    cy.findByText("My Profile").click();
    cy.findByRole("button", { name: "Change Password" }).should("be.visible");
    cy.get("body").then(($body) => {
        if ($body.find('button:contains("Generate API Key")').length > 0) {
            // If Generate API Key button exists, click it
            cy.findByRole("button", { name: "Generate API Key" }).click();
        } else {
            // If Generate API Key doesn't exist, click Show API Key instead
            cy.findByRole("button", { name: "Show API Key" }).click();
        }
    });
    // Check for Hide API Key button (which appears after showing)
    cy.findByRole("button", { name: "Hide API Key" }).should("be.visible");

    // Check for Revoke API Key button
    cy.findByRole("button", { name: "Revoke API Key" }).should("be.visible");

    // Find the Copy to clipboard button using a more resilient approach
    cy.findAllByText("Copy to clipboard").should("be.visible");
    cy.findByText("Theme Mode").should("be.visible");
}

// functions related to content
export function accessCompliance() {
    const extractedText = extractTextFromUrl(Cypress.env("credentials").DATABEE_UI);
    if (extractedText === "sales-2") {
        cy.navigateTopNav("Content");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading Content Delivery Options...");
    } else {
        cy.navigateTopNav("Compliance", "Reports");
        cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading CCM Report List...");
    }
    cy.findByLabelText("Purchase Status:")
        .should("be.visible")
        .parents()
        .find(".react-select__single-value")
        .first()
        .should("be.visible")
        .invoke("text")
        .then((text) => {
            expect(["All", "Purchased"]).to.include(text);
        });
}
