/// <reference types="cypress"/>

import { navigateToRemediationActions } from "../remediation_actions/remediation_action_utils";

describe("Tenant: User can access remediation actions page", { tags: ["compliance", "full", "demo_staging"] }, () => {
    it("Admin can access remediation actions page", () => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
        navigateToRemediationActions();
        cy.clickLogout();
    });
});
