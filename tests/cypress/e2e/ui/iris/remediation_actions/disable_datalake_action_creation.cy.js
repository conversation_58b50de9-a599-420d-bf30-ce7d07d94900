/// <reference types="cypress"/>
import { changeDataLakeState, navigateToDataLakeConfig } from "../configuration/config_utils";

import { navigateToRemediationActions } from "./remediation_action_utils";

describe(
    "Create Remediation Action | DataLake Disabled",
    { tags: ["remediation_actions", "full", "regression"] },
    () => {
        before(() => {
            cy.clearCookies();
            cy.visit(Cypress.env("credentials").DATABEE_UI);
            cy.manualLogin(
                Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
            );
            navigateToDataLakeConfig();
            changeDataLakeState("Disable");
        });

        it("should create new action when data lake is disabled", () => {
            navigateToRemediationActions();
            cy.reload();
            cy.contains("No datalake connection established yet").should("be.visible");
        });

        after(() => {
            navigateToDataLakeConfig();
            changeDataLakeState("Enable");
            cy.clickLogout();
            cy.clearCookies();
        });
    }
);
