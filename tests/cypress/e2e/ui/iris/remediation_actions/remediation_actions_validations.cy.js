/// <reference types="cypress"/>
import {
    configureServiceNowIntegration,
    createNewAction,
    getDatalakeSchemas,
    getFormattedNYTimePlusMinutes,
    navigateToRemediationActions,
    openCreateNewActionDialog,
    updateSnowflakeDatabase,
    validateSchemasAndTablesInForm,
    validateServiceNowIntegrationFields,
    waitForActionToComplete,
    waitForActionToCreate,
} from "./remediation_action_utils";

describe("Tenant: User can access remediation actions feature", { tags: ["remediation_actions", "full"] }, () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.visit(Cypress.env("credentials").DATABEE_UI);
        cy.manualLogin(
            Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
            Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
        );
    });

    afterEach(() => {
        cy.clickLogout();
    });

    const createdActions = [];

    it("Admin Can Create ServiceNow Integrations for Remediation Actions", () => {
        cy.navigateTopRightNav("Configuration", "System", "Integrations");
        cy.contains("h2", "Integrations").should("be.visible");
        cy.get("div").contains("ServiceNow").click();
        const authType = Cypress.env("credentials").SERVICENOW_CREDENTIALS.AUTH_TYPE;
        const username = Cypress.env("credentials").SERVICENOW_CREDENTIALS.USERNAME;
        const password = Cypress.env("credentials").SERVICENOW_CREDENTIALS.PASSWORD;
        const clientKey = Cypress.env("credentials").SERVICENOW_CREDENTIALS.CLIENT_KEY;
        const clientSecret = Cypress.env("credentials").SERVICENOW_CREDENTIALS.CLIENT_SECRET;
        const tokenUrl = Cypress.env("credentials").SERVICENOW_CREDENTIALS.TOKEN_URL;
        configureServiceNowIntegration(authType, username, password, clientKey, clientSecret, tokenUrl);
    });

    it("Admin Can Retrieve Saved Connection Detail Of ServiceNow Integrations", () => {
        const MASKED = "****";
        cy.intercept("GET", "/api/config/system/integrations/").as("integrationDetailsAPI");
        cy.navigateTopRightNav("Configuration", "System", "Integrations");
        cy.contains("h2", "Integrations").should("be.visible");
        cy.wait("@integrationDetailsAPI").then((interception) => {
            cy.log("Intercepted API Response:", interception.response.body);
            expect(interception.response.statusCode).to.eq(200);
            const response = interception.response.body;
            expect(response.items[0].password).to.eq(MASKED);
            expect(response.items[0].client_secret).to.eq(MASKED);
        });
        cy.get("div").contains("ServiceNow").click();
        const authType = Cypress.env("credentials").SERVICENOW_CREDENTIALS.AUTH_TYPE;
        const username = Cypress.env("credentials").SERVICENOW_CREDENTIALS.USERNAME;
        const clientKey = Cypress.env("credentials").SERVICENOW_CREDENTIALS.CLIENT_KEY;
        const tokenUrl = Cypress.env("credentials").SERVICENOW_CREDENTIALS.TOKEN_URL;
        validateServiceNowIntegrationFields(authType, username, clientKey, tokenUrl);
        cy.get("body").type("{esc}");
    });

    it("Admin Can Save ServiceNow Integrations Without Test Connection", () => {
        cy.navigateTopRightNav("Configuration", "System", "Integrations");
        cy.contains("h2", "Integrations").should("be.visible");
        cy.get("div").contains("ServiceNow").click();
        const authType = Cypress.env("credentials").SERVICENOW_CREDENTIALS.AUTH_TYPE;
        const username = Cypress.env("credentials").SERVICENOW_CREDENTIALS.USERNAME;
        const password = Cypress.env("credentials").SERVICENOW_CREDENTIALS.PASSWORD;
        const clientKey = Cypress.env("credentials").SERVICENOW_CREDENTIALS.CLIENT_KEY;
        const clientSecret = Cypress.env("credentials").SERVICENOW_CREDENTIALS.CLIENT_SECRET;
        const tokenUrl = Cypress.env("credentials").SERVICENOW_CREDENTIALS.TOKEN_URL;
        configureServiceNowIntegration(authType, username, password, clientKey, clientSecret, tokenUrl, false);
    });

    it("Verify The Creation Of New Actions", () => {
        navigateToRemediationActions();
        const actionName = `automation-test-${Date.now()}`;
        openCreateNewActionDialog();
        createNewAction(
            "ServiceNow",
            actionName,
            "Description Text",
            "Continuous Controls Monitoring (CCM)",
            "Mfa",
            { "Application Id": { IsNotNone: true } },
            "Ticket Title",
            "Ticket Description",
            '{"table": "incident"}',
            "12:00 am",
            "All",
            true
        );
        createdActions.push(actionName);
    });

    it("Verify The Creation Of New Actions via POST API and Verify Action Should run successfully at defined schedule", () => {
        // Step 1: Get auth token
        cy.request({
            method: "POST",
            url: `${Cypress.env("credentials").TENANT_API}/login/`,
            headers: { "Content-Type": "application/json" },
            body: {
                username: Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
                password: Cypress.env("credentials").DATABEE_ADMIN_PASSWORD,
            },
        })
            .its("body.token.access")
            .then((token) => {
                const actionName = `automation-test-${Date.now()}`;
                const expectedRunTime = getFormattedNYTimePlusMinutes(2);

                // Step 2: Get cron schedule
                cy.getCronAfterMinutes(2).then((cronSchedule) => {
                    cy.log("Cron schedule:", cronSchedule);

                    // Step 3: Create action via API
                    const payload = {
                        properties: {},
                        data: {
                            action_id: "",
                            action_name: actionName,
                            connection_name: "ServiceNow",
                            connection_type: null,
                            is_active: false,
                            is_paused: false,
                            last_fetch_status: null,
                            last_run: null,
                            next_run: null,
                            updated_by: "",
                            updated_at: null,
                            description: `${actionName} description`,
                            view: "vw_org_hierarchy",
                            query_schema: "BDW",
                            query_string: "employee_id isnot None",
                            incident_title: `Automation-${actionName}`,
                            incident_description: "This is an automated incident created by Cypress test",
                            schedule_interval: cronSchedule,
                            datalake_type: "snowflake",
                            additional_fields: { table: "incident" },
                        },
                    };

                    cy.request({
                        method: "POST",
                        url: `${Cypress.env("credentials").TENANT_API}/remediation-actions/connections/actions/`,
                        headers: {
                            "Content-Type": "application/json",
                            Authorization: `Bearer ${token}`,
                        },
                        body: payload,
                    }).then((res) => {
                        expect(res.status).to.eq(200);
                        createdActions.push(actionName);
                    });

                    // Step 4: Validate action in UI
                    navigateToRemediationActions();
                    waitForActionToCreate(actionName);
                    waitForActionToComplete(actionName, "Completed");
                    cy.findByText(actionName).should("exist");
                    cy.contains("Searching...").should("not.exist");

                    // Step 5: Assert action row details
                    cy.get("#actions-table tbody tr").each(($row) => {
                        cy.wrap($row).within(() => {
                            cy.get("td")
                                .eq(1)
                                .then(($cell) => {
                                    if ($cell.text().includes(actionName)) {
                                        cy.get("td").eq(1).should("contain", actionName);
                                        cy.get("td").eq(2).should("contain", "ServiceNow");
                                        cy.get("td").eq(3).should("contain", "Active");
                                        cy.get("td")
                                            .eq(4)
                                            .should(($td) => {
                                                expect($td.text()).to.contain("Completed");
                                            });
                                        cy.get("td")
                                            .eq(5)
                                            .should(($td) => {
                                                expect($td.text()).to.contain(expectedRunTime);
                                            });
                                    }
                                });
                        });
                    });
                });
            });
    });

    it("Validate Updating an existing action - change the Schedule", () => {
        navigateToRemediationActions();
        cy.contains("Searching...").should("not.exist");
        const actionName = createdActions[0];
        const updatedSchedule = "7:00 am";
        cy.findByText(actionName).click({ force: true });
        cy.get(".modal-body").within(() => {
            cy.findByRole("button", { name: "Next" }).scrollIntoView().should("be.visible").click({ force: true });
        });
        cy.contains("label", "Schedule").parent().find(".react-select__control").click();
        cy.get(".react-select__option").contains(updatedSchedule).click({ force: true });
        cy.findByRole("button", { name: "Save" }).click();
        cy.validateToastMessage("Action updated successfully");
        cy.reload();
        cy.contains("Searching...").should("not.exist");
        waitForActionToComplete(actionName, "Active", 3);
        cy.reload();
        cy.contains("Searching...").should("not.exist");
        cy.findByPlaceholderText("Search by action name")
            .click({ force: true })
            .clear()
            .type(actionName.match(/\d+$/)?.[0]);
        cy.findByText(actionName)
            .parent()
            .parent()
            .within(() => {
                cy.get("td").eq(1).should("contain", actionName);
                cy.get("td").eq(2).should("contain", "ServiceNow");
                cy.get("td").eq(3).should("contain", "Active");
                cy.get("td")
                    .eq(6)
                    .should(($td) => {
                        expect($td.text()).to.contain(updatedSchedule);
                    });
            });
    });

    it("Verify all schemas present in datalake displayed in the dropdown and verify the table/views corresponding to the selected schema are displayed in the table dropdown", () => {
        getDatalakeSchemas().then((schemas) => {
            validateSchemasAndTablesInForm(schemas);
        });
    });

    it("Verify the new action form data is populated based on the updated datalake configuration only", () => {
        updateSnowflakeDatabase(Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.ALTERNATE_DATABASE);
        getDatalakeSchemas().then((schemas) => {
            validateSchemasAndTablesInForm(schemas);
        });
        updateSnowflakeDatabase(Cypress.env("credentials").SNOWFLAKE_CREDENTIALS.DATABASE);
    });

    it("Verify Tickets are not created in the servicenow when the action fetched zero records", () => {
        navigateToRemediationActions();
        cy.contains("Searching...").should("not.exist");
        cy.findByRole("button", { name: "History" }).click();
        cy.contains("Searching...").should("not.exist");
        cy.contains("h2", "Action History")
            .should("be.visible")
            .then(() => {
                cy.get("#actions-history-table tbody tr").each(($row) => {
                    cy.wrap($row).within(() => {
                        cy.get("td")
                            .eq(5)
                            .invoke("text")
                            .then((totalRecords) => {
                                if (totalRecords.trim() === "0") {
                                    cy.get("td").eq(6).should("have.text", ""); // Ticket Number column is empty
                                    cy.get("td").eq(7).should("contain.text", "Success"); // Run Info contains Success
                                }
                            });
                    });
                });
            });
    });

    it("Verify the action results are attached in the service now ticket as csv", () => {
        const actionName = createdActions[1];
        let serviceNowIncidentTicket = "";
        navigateToRemediationActions();
        cy.contains("Searching...").should("not.exist");
        cy.findByRole("button", { name: "History" }).click();
        cy.contains("Searching...").should("not.exist");
        cy.contains("h2", "Action History").should("be.visible");
        cy.findByPlaceholderText("Search by action name")
            .click({ force: true })
            .clear()
            .type(actionName.match(/\d+$/)?.[0]);
        cy.get("#actions-history-table tbody tr")
            .first()
            .within(() => {
                cy.get("td").eq(0).should("contain", actionName);
                cy.get("td")
                    .eq(5)
                    .invoke("text")
                    .then((totalRecords) => {
                        if (Number(totalRecords) === 0) {
                            throw new Error("No records found, skipping ticket validation");
                        } else {
                            expect(Number(totalRecords)).to.gt(0); // Total Records should be greater than 0
                        }
                    });
                cy.get("td")
                    .eq(6)
                    .invoke("text")
                    .then((ticket) => {
                        serviceNowIncidentTicket = ticket.trim();
                        expect(serviceNowIncidentTicket).to.contain("INC"); // Ticket Number should not be empty
                        const instanceUrl = Cypress.env("credentials").SERVICENOW_CREDENTIALS.TOKEN_URL;
                        const username = Cypress.env("credentials").SERVICENOW_CREDENTIALS.USERNAME;
                        const password = Cypress.env("credentials").SERVICENOW_CREDENTIALS.PASSWORD;
                        cy.request({
                            method: "GET",
                            url: `${instanceUrl}/api/now/table/incident?sysparm_query=number=${serviceNowIncidentTicket}`,
                            auth: { username, password },
                        }).then((incidentRes) => {
                            expect(incidentRes.status).to.eq(200);
                            expect(incidentRes.body.result).to.have.length.greaterThan(0);
                        });
                    });
            });
    });
});
