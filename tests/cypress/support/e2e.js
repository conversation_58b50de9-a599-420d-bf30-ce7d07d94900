// ***********************************************************
// This file is processed and
// loaded automatically before your test files.
// ***********************************************************

import "./commands";
// eslint-disable-next-line import/no-unresolved -- Correct Import is use
import "allure-cypress/commands";

import registerCypressGrep from "@cypress/grep";

Cypress.on("test:after:run", (test, runnable) => {
    if (test.state === "failed") {
        let item = runnable;
        const nameParts = [runnable.title];

        // Iterate through all parents and grab the titles
        while (item.parent) {
            nameParts.unshift(item.parent.title);
            item = item.parent;
        }
    }
});

Cypress.on("uncaught:exception", (err) => {
    // eslint-disable-next-line no-console -- Need to disable this rule to print error to console
    console.error(err.message);
    return false;
});

registerCypressGrep();

require("cy-verify-downloads").addCustomCommand();
