const fs = require("fs");
const {
    S3Client,
    DeleteBucketPolicyCommand,
    PutBucketPolicyCommand,
    GetBucketNotificationConfigurationCommand,
    PutBucketNotificationConfigurationCommand,
    PutObjectCommand,
    DeleteObjectsCommand,
    CreateBucketCommand,
    ListBucketsCommand,
    DeleteBucketCommand,
    ListObjectsV2Command,
} = require("@aws-sdk/client-s3");

class S3Service {
    constructor(awsRegion, s3BucketParams) {
        const config = {
            region: awsRegion,
        };

        // Check if running in GitHub Actions environment
        const isGitHubWorkflow = !!process.env.GITHUB_ACTIONS;

        // Add credentials only if in GitHub workflow
        if (isGitHubWorkflow) {
            config.credentials = {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
                sessionToken: process.env.AWS_SESSION_TOKEN,
            };
        }
        this.s3Client = new S3Client(config);
        this.s3BucketParams = s3BucketParams;
    }

    /**
     * Retrieves a list of objects in the S3 bucket.
     *
     * - Uses the S3 client to send a `ListObjectsV2Command` request to fetch all objects in the specified bucket.
     *
     * @returns {String} Throws an error if the listing operation fails.
     *
     * @example
     * const s3Service = new S3Service('us-west-2', 'my-bucket');
     * s3Service.listObjects()
     *     .then(objects => console.log(objects))
     *     .catch(error => console.error(error));
     */
    async listObjects() {
        const listParams = { Bucket: this.s3BucketParams.Bucket };
        const listCommand = new ListObjectsV2Command(listParams);
        try {
            const listResponse = await this.s3Client.send(listCommand);
            return listResponse.Contents || [];
        } catch (error) {
            return `Error listing objects: ${error.message}`;
        }
    }

    // Adds or updates an access policy for an S3 bucket.
    async addBucketPolicyPermissions(policy) {
        try {
            const policyParams = {
                Bucket: this.s3BucketParams.Bucket,
                Policy: policy,
            };
            const policyCommand = new PutBucketPolicyCommand(policyParams);
            return await this.s3Client.send(policyCommand);
        } catch (error) {
            return { error };
        }
    }

    // Configures or updates notification settings for an S3 bucket. Allows specifying
    // which events trigger notifications at the destination SQS.
    async createEventNotification(QueueConfig) {
        try {
            const notificationParams = {
                Bucket: this.s3BucketParams.Bucket,
                NotificationConfiguration: {
                    QueueConfigurations: [QueueConfig],
                },
            };

            const notificationCommand = new PutBucketNotificationConfigurationCommand(notificationParams);
            return await this.s3Client.send(notificationCommand);
        } catch (error) {
            return { error };
        }
    }

    // Below the function to connect the AWS S3
    // And will retrieve the list of buckets from AWS S3 if not exists it will create a new bucket
    async createS3Bucket() {
        try {
            const listCommand = new ListBucketsCommand({});
            const listBuckets = await this.s3Client.send(listCommand);
            if (listBuckets.Buckets.find((item) => item.Name === this.s3BucketParams.Bucket)) {
                return `Bucket is already exists.\n${this.s3BucketParams.Bucket}`;
            }
            const createCommand = new CreateBucketCommand(this.s3BucketParams);
            return await this.s3Client.send(createCommand);
        } catch (error) {
            return { error };
        }
    }

    // Takes notificationId and deletes the existing SQS notification configuration.
    async deleteEventNotification(notificationId) {
        try {
            const getConfigCommand = new GetBucketNotificationConfigurationCommand({
                Bucket: this.s3BucketParams.Bucket,
            });
            const currentConfig = await this.s3Client.send(getConfigCommand);

            // Filter out the notification configuration you want to remove by ID
            const queueConfig = {
                QueueConfigurations: currentConfig.QueueConfigurations.filter(
                    (notificationConfig) => notificationConfig.Id !== notificationId
                ),
            };

            const queueParams = {
                Bucket: this.s3BucketParams.Bucket,
                NotificationConfiguration: queueConfig,
            };
            const deleteNotificationCommand = new PutBucketNotificationConfigurationCommand(queueParams);
            return await this.s3Client.send(deleteNotificationCommand);
        } catch (error) {
            return { error };
        }
    }

    // Function to delete files objects from S3
    async deleteFilesFromS3Bucket(filesToDelete) {
        const objectsToDelete = filesToDelete.map((fileName) => ({ Key: fileName }));
        const deleteParams = {
            Bucket: this.s3BucketParams.Bucket,
            Delete: { Objects: objectsToDelete },
        };

        try {
            return await this.s3Client.send(new DeleteObjectsCommand(deleteParams));
        } catch (error) {
            return { error };
        }
    }

    // Deletes the access policy attached to an S3 bucket, effectively resetting its permissions.
    async deleteBucketPolicyPermissions() {
        try {
            const command = new DeleteBucketPolicyCommand(this.s3BucketParams);
            return await this.s3Client.send(command);
        } catch (error) {
            return { error };
        }
    }

    /**
     * Deletes an S3 bucket and its contents, including any event notifications and bucket policies.
     *
     * - Lists and deletes all objects in the bucket.
     * - Retrieves and clears any existing event notification configurations.
     * - Deletes any bucket policies attached to the bucket.
     * - Finally, deletes the S3 bucket itself.
     *
     * @returns {String} Throws an error if any of the deletion steps fail.
     *
     * @example
     * const s3Service = new S3Service('us-west-2', 'my-bucket');
     * s3Service.deleteS3Bucket()
     *     .then(response => console.log(response))
     *     .catch(error => console.error(error));
     */

    async deleteS3Bucket() {
        try {
            // List all buckets
            const listBucketsResponse = await this.s3Client.send(new ListBucketsCommand({}));
            const matchingBuckets = listBucketsResponse.Buckets.filter((bucket) =>
                bucket.Name.startsWith("databee-cypress")
            );

            // Process all buckets in parallel using Promise.all
            const deletionResults = await Promise.all(
                matchingBuckets.map(async (bucket) => {
                    const bucketName = bucket.Name;

                    try {
                        // Step 1: Delete all objects
                        const listObjectsResponse = await this.s3Client.send(
                            new ListObjectsV2Command({ Bucket: bucketName })
                        );

                        if (listObjectsResponse.Contents?.length > 0) {
                            await this.s3Client.send(
                                new DeleteObjectsCommand({
                                    Bucket: bucketName,
                                    Delete: {
                                        Objects: listObjectsResponse.Contents.map((obj) => ({ Key: obj.Key })),
                                    },
                                })
                            );
                        }

                        // Step 2: Remove bucket notification config
                        try {
                            const getConfigCommand = new GetBucketNotificationConfigurationCommand({
                                Bucket: bucketName,
                            });
                            const currentConfig = await this.s3Client.send(getConfigCommand);

                            if (currentConfig.QueueConfigurations?.length > 0) {
                                const clearConfigCommand = new PutBucketNotificationConfigurationCommand({
                                    Bucket: bucketName,
                                    NotificationConfiguration: { QueueConfigurations: [] },
                                });
                                await this.s3Client.send(clearConfigCommand);
                            }
                        } catch (notificationErr) {
                            // Silently handle notification config errors
                        }

                        // Step 3: Delete bucket policy
                        try {
                            await this.s3Client.send(new DeleteBucketPolicyCommand({ Bucket: bucketName }));
                        } catch (policyErr) {
                            if (policyErr.name !== "NoSuchBucketPolicy") {
                                // Silently handle policy errors other than "NoSuchBucketPolicy"
                            }
                        }

                        // Step 4: Delete the bucket
                        await this.s3Client.send(new DeleteBucketCommand({ Bucket: bucketName }));

                        return { name: bucketName, success: true };
                    } catch (bucketError) {
                        return { name: bucketName, success: false, error: bucketError.message };
                    }
                })
            );

            const successCount = deletionResults.filter((result) => result.success).length;
            const prefix = "databee-cypress";

            return {
                success: `Deleted ${successCount} of ${matchingBuckets.length} buckets with prefix "${prefix}"`,
                details: deletionResults,
            };
        } catch (error) {
            return { error: `Error listing S3 buckets: ${error.message}` };
        }
    }

    // Retrieve the files from the S3 bucket and prefix is an optional.
    async getFilesFromS3(prefix = "") {
        const files = [];
        try {
            const listParams = {
                Bucket: this.s3BucketParams.Bucket,
                Prefix: prefix,
            };
            const listCommand = new ListObjectsV2Command(listParams);
            const listResponse = await this.s3Client.send(listCommand);

            if (listResponse.Contents) {
                listResponse.Contents.forEach((item) => {
                    files.push({ key: item.Key });
                });
            }
        } catch (error) {
            return { error };
        }
        return files;
    }

    // to upload files to S3
    async uploadFileToS3(filesToUpload) {
        const listParams = {
            Bucket: this.s3BucketParams.Bucket,
        };
        const listCommand = new ListObjectsV2Command(listParams);
        const listResponse = await this.s3Client.send(listCommand);

        const existingFiles = listResponse.Contents ? listResponse.Contents.map((item) => item.Key) : [];

        const failedUpload = [];
        const successfulUpload = [];

        await Promise.all(
            filesToUpload.map(async (filePath) => {
                const fileName = filePath.split("/").pop();
                if (!existingFiles.includes(fileName)) {
                    let fileContent = "";
                    try {
                        fileContent = fs.readFileSync(filePath);
                        const command = new PutObjectCommand({
                            Bucket: this.s3BucketParams.Bucket,
                            Key: fileName,
                            Body: fileContent,
                        });
                        await this.s3Client.send(command);
                        successfulUpload.push(fileName);
                    } catch (error) {
                        failedUpload.push(filePath);
                    }
                } else {
                    successfulUpload.push(fileName); // Already exists, consider as successful
                }
            })
        );

        return { success: successfulUpload, fail: failedUpload };
    }
}
module.exports = S3Service;
