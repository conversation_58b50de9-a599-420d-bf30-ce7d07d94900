import "@testing-library/cypress/add-commands";
/**
 * Command that simulates the user logging out from the web application by clicking
 * on the logout button. This functionality is required by some UI tests that require
 * users to logout and log back in.
 * @param {string} username account email
 * @param {string} password account password
 * @param {number} waitSecs - The time to wait in seconds for the DOM to load before checking for text existence (default is 2 seconds).
 * @param {number} maxWaitSecs - Optional. The maximum time to wait for the condition in seconds. Default is 30 seconds.

 */
Cypress.Commands.add("manualLogin", (username, password, waitSecs = 5, maxWaitSecs = 100) => {
    let attempt = 1;
    const totalAttempts = Math.ceil(maxWaitSecs / waitSecs);

    function checkLoginStatus() {
        cy.wait(waitSecs * 1000).then(() => {
            cy.url().then((url) => {
                if (url.includes("/login")) {
                    cy.get("body").then(($body) => {
                        if ($body.find("#login-error-container").length) {
                            cy.get("#login-error-container").then(($errorContainer) => {
                                const errorText = $errorContainer.text();
                                Cypress.env("loginFailed", true);
                                cy.log(`Login failed: ${errorText}`);
                            });
                        } else if (attempt < totalAttempts) {
                            attempt += 1;
                            checkLoginStatus();
                        } else {
                            Cypress.env("loginFailed", true);
                            throw new Error(`Login failed after ${attempt * waitSecs} seconds.`);
                        }
                    });
                } else {
                    Cypress.env("loginFailed", false);
                }
            });
        });
    }
    cy.findByPlaceholderText("Username").type(username);
    cy.findByPlaceholderText("Password").type(password).type("{enter}");
    checkLoginStatus();
});

/**
 * Command that simulates the user logging out from the web application by clicking
 * on the logout button. This functionality is required by some UI tests that require
 * users to logout and log back in.
 *
 * The function checks if the current test has passed before attempting to log out.
 * If the logout button is not immediately visible, it retries based on the specified wait times.
 */
Cypress.Commands.add("clickLogout", function clickLogoutCommand(waitSecs = 5, maxWaitSecs = 100) {
    let attempt = 1;
    const totalAttempts = Math.ceil(maxWaitSecs / waitSecs);

    function attemptLogout() {
        if (!Cypress.env("loginFailed")) {
            cy.findByTitle("My Profile")
                .parent()
                .within(($el) => {
                    cy.wrap($el).click();
                    cy.findByText("Logout").click();
                });

            cy.wait(waitSecs * 1000).then(() => {
                cy.get("#submit-login-btn").then(($elem) => {
                    if ($elem.is(":visible")) {
                        cy.wrap($elem).contains("Login").should("be.visible");
                        Cypress.env("logoutFailed", false);
                    } else if (attempt < totalAttempts) {
                        attempt += 1;
                        attemptLogout();
                    } else {
                        Cypress.env("logoutFailed", true);
                        throw new Error(`Logout failed after ${attempt * waitSecs} seconds.`);
                    }
                });
            });
        }
    }
    if (this.currentTest && this.currentTest.state === "passed") {
        attemptLogout();
    } else {
        cy.log("Skipping logout due to test failure or no current test context.");
    }
});

/**
 * This commands intercepts all dashboards API requests and waits for them to load
 */
Cypress.Commands.add("waitForDashboardsToLoad", () => {
    // waiting for all the widgets to load
    cy.intercept("/api/console/**").as("getDashboards");
    cy.visit("/console");
    cy.wait("@getDashboards");
});

/**
 * This command validates API responses and ensures that only the
 * expected keys are present
 * @param {string} resource The REST API endpoint URL resource
 * @param {string} method The REST method (e.g. GET, POST, PUT, etc.)
 * @param {Array} expectedKeys The keys that are expected to be in the response body
 * @param {dict} options dictionary override to the default request options. Useful
 *                       for adding a body, additional headers or parameter query strings
 */
Cypress.Commands.add("validateAPIResponse", (resource, method, expectedKeys, options = {}) => {
    cy.get("@apiKey").then((token) => {
        const defaultRequestOptions = {
            method,
            url: `${Cypress.env("credentials").TENANT_API}${resource}`,
            headers: { Authorization: `Token ${token}` },
        };
        cy.request({ ...defaultRequestOptions, ...options })
            .its("body")
            .then((response) => {
                if (response) {
                    if (Array.isArray(response)) {
                        // Check the first record in the array has all the expected keys
                        expect(response[0]).to.have.all.deep.keys(expectedKeys);
                    } else {
                        window.console.log("this is an object, check all the keys");
                        expect(response).to.have.all.deep.keys(expectedKeys);
                    }
                } else {
                    // DELETE endpoints will sometime return data without a response body
                    // eslint-disable-next-line no-unused-expressions -- suppress no unused expressions
                    expect(response).to.be.empty;
                }
            });
    });
});

Cypress.Commands.add("getNotificationId", (notificationTitle = "") => {
    cy.get("@apiKey").then((apiKey) => {
        cy.request({
            method: "GET",
            url: `${Cypress.env("credentials").TENANT_API}/notifications/`,
            headers: { Authorization: `Token ${apiKey}` },
        }).then((response) => {
            if (notificationTitle.length === 0) {
                // specific content not requested. Return the first note id
                return response.body[0].id;
            }
            for (let i = 0; i < response.body.length; i += 1) {
                if (response.body[i].title.includes(notificationTitle)) {
                    return response.body[i].id;
                }
            }
            return null;
        });
    });
});

/**
 * Logs the current user out and logs in with the given user credentials
 * @param {string} username account email
 * @param {string} password account password
 */
Cypress.Commands.add("switchUser", (username = "", password = "") => {
    // POST api/logout/ revokes a user’s JWT token
    cy.get("@apiKey").then((apiKey) => {
        cy.request({
            method: "POST",
            url: `${Cypress.env("credentials").TENANT_API}/logout/`,
            headers: { Authorization: `Token ${apiKey}` },
        }).then((response) => {
            expect(response.status).to.eq(200);
            // Important to clear out the cookies otherwise the subsequent
            // tests will use invalid tokens
            cy.clearCookies();

            // POST api/login/ logs us in without needing the UI - uses the logout
            // endpoint with the user's API key, not the browser's JWT token
            cy.request("POST", `${Cypress.env("credentials").TENANT_API}/login/`, {
                username,
                password,
            })
                .its("body")
                .then((_response) => {
                    // Store the token pair associated with the user that is now logged in
                    cy.setCookie("ath-access-token", _response.token.access);
                    cy.setCookie("ath-refresh-token", _response.token.refresh);

                    // Store this user's api key as a variable (in case the test calls
                    // this switch user command again)
                    cy.wrap(_response.api_key).as("apiKey");
                });
        });
    });
});

// eslint-disable-next-line import/prefer-default-export -- disabling since it's a legacy comment
export function compareImages(img1, img2) {
    if (img1.data.length !== img2.data.length) {
        return false;
    }
    for (let i = 0; i < img1.data.length; i += 1) {
        if (img1.data[i] !== img2.data[i]) {
            return false;
        }
    }
    return true;
}

/**
 * For given dashboard slug , gets the widgets first id
 * @param {string} dashboardSlug- Name of the existing slug
 */
Cypress.Commands.add("getDashboardComponentsId", (dashboardSlug = "") => {
    cy.get("@token").then((token) => {
        cy.request({
            method: "GET",
            url: `${Cypress.env("credentials").TENANT_API}/console/reports/${dashboardSlug}/components/`,
            headers: { Authorization: `Bearer ${token.access}` },
        }).then(
            (response) =>
                // Returns the first widget id
                response.body[0].id
        );
    });
});

/**
 * This command exists for an select input field built with the `react-select`
 * library. For the purposes of cypress testing this tests does not behave
 * like a regular dropdown element nor a text field. In order to get the field
 * to truly behave the way it does for a user we need to run a bit of extra
 * steps, which are captured by this method.
 *
 * @param {string} element id or class name specific to the react-select tests
 * @param {string} value value you want to simulate the user selecting
 */
Cypress.Commands.add("setReactSelectOption", (element, value) => {
    cy.get(element).find("input").eq("0").type(value, { force: true }).type("{enter}", { force: true });
});

/**
 * This command exists for an entering input field built with the `react-select`
 * library, normal inputs and checkbox option.
 * For the purposes of cypress testing this tests does not behave
 * like a text field. In order to get the field
 * to truly behave the way it does for a user we need to run a bit of extra
 * steps, which are captured by this method.
 *
 * @param {string} label selector name specific to the web page elements
 * @param {string} value val you want to simulate the user entering
 */
Cypress.Commands.add("setInput", (label, value) => {
    cy.findAllByLabelText(label).should("exist").first().invoke("attr", "id").as("getById");
    cy.findAllByLabelText(label).should("exist").first().invoke("attr", "type").as("getByType");
    cy.findAllByLabelText(label).should("exist").first().invoke("attr", "class").as("getByClass");

    cy.get("@getById").then((selectorId) => {
        cy.get("@getByType").then((inputType) => {
            cy.get("@getByClass").then((className) => {
                const element = cy.get(`[id="${selectorId}"]`);

                if (
                    (className !== "react-select__input" && inputType === "text") ||
                    inputType === "number" ||
                    inputType === "textfield" ||
                    inputType === "password"
                ) {
                    element.clear().type(value, { force: true });
                } else if (inputType === "checkbox" || inputType === "radio") {
                    element.scrollIntoView().then(($el) => {
                        if (value) {
                            cy.wrap($el).uncheck({ force: true }).should("not.be.checked");
                        } else {
                            cy.wrap($el).check({ force: true }).should("be.checked");
                        }
                    });
                } else if (className === "react-select__input") {
                    cy.document().then((doc) => {
                        if (doc.querySelector(".modal-dialog.modal-lg")) {
                            // Element is inside a dialog modal
                            cy.get(".modal-dialog.modal-lg")
                                .find(`[class*="${className}"]`)
                                .find(`[id="${selectorId}"]`)
                                .focus()
                                .type(value, { force: true })
                                .type("{enter}", { force: true });
                        } else {
                            // Element is on the web page
                            element.type(value);
                            cy.get(".react-select__option").contains(value, { matchCase: false }).first().click();
                        }
                    });
                } else {
                    // Fallback for elements without type or class
                    element.clear().type(value, { force: true });
                }
            });
        });
    });
});

/**
 * Iterate to enter the input field in the react-select component and normal inputs.
 * @param {dict} formData you want to provide dictionary object with key/value pair
 */
Cypress.Commands.add("updateForm", (formData) => {
    Object.entries(formData).forEach(([key, value]) => {
        cy.setInput(key, value);
    });
});

/**
 * retryTextNotContainCheck function to find an element not containing specific text.
 * @param {selector} selector - The selector find the elements.
 * @param {string} elemText - The text to search for within the elements.
 * @param {number} waitSecs - Optional. The time to wait between retries in seconds. Default is 5 seconds.
 * @param {number} maxWaitSecs - Optional. The maximum time to wait for the condition in seconds. Default is 120 seconds.
 */
Cypress.Commands.add("retryTextNotContainCheck", (selector, elemText, waitSecs = 5, maxWaitSecs = 120) => {
    let attempt = 0;

    const checkVisibility = () =>
        cy.checkElementIsVisible(selector).then((isExist) => {
            if (isExist) {
                cy.get(selector).then(($elem) => {
                    const textContent = $elem.text();
                    if (!textContent.includes(elemText)) {
                        return cy.findByText(elemText).should("not.exist");
                    }
                    attempt += 1;
                    if (attempt >= maxWaitSecs / waitSecs) {
                        throw new Error("Max wait time exceeded");
                    } else {
                        return cy.wait(waitSecs * 1000).then(() => checkVisibility());
                    }
                });
            }
        });

    return checkVisibility();
});

/**
 * Custom command for clicking the "View Details" button in a table cell based on a given label and value.
 * This command is designed to interact with a table built with the normal components,
 * ensuring accurate behavior during Cypress testing.
 *
 * @param {string} label The selector name specific to the table elements on the web page.
 * @param {string} value The value to simulate the user's click on the "View Details" button.
 * @param {string} selectorType Optional. The type of selector to use (default is 'text').
 */
Cypress.Commands.add("clickViewDetails", (label, value, selectorType = "text") => {
    const failedPages = [];

    cy.findByRole("table").find("th").as("headers");

    cy.get("@headers")
        .should("exist")
        .then(($headers) => {
            const $column = $headers.filter(`:contains(${label})`);
            expect($column).to.have.length.at.least(0);
            const columnIndex = $column.index();

            cy.findByRole("table")
                .find("tbody tr")
                .first()
                .find("td")
                .eq(columnIndex)
                .scrollIntoView()
                .should("be.visible")
                .within(() => {
                    if (selectorType === "text") {
                        cy.findByText(value).should("be.visible").click({ force: true });
                    } else {
                        cy.findByTitle(value).should("be.visible").click({ force: true });
                    }
                });
        });

    // After click, wait for loading to complete
    cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading...");

    // Capture URL and check for errors
    cy.url().then((url) => {
        // Use a descriptive name based on the clicked item
        const pageDescription = `${label}`;
        cy.checkForErrors(`Click View Details: ${pageDescription}`, failedPages, url);

        // Handle any errors found
        cy.then(() => {
            if (failedPages.length > 0) {
                const errorSummary = failedPages
                    .map((page) => `Page: ${page.page}\nError: ${page.message}\nURL: ${page.url || "N/A"}`)
                    .join("\n");

                throw new Error(`Navigation Errors:\n${errorSummary}`);
            }
        });
    });
});

/**
 * Set multiple values for a multi-select input field.
 * @param {string} selector - The selector of the multi-select input field
 * @param {Array<string>} values - The values to be set
 */
Cypress.Commands.add("setMultiSelectValues", (selector, values) => {
    values.forEach((value) => {
        cy.get(`[id*="${selector}"]`).type(`${value}{enter}`);
    });
});

/**
 * Function to find column index by header label and perform an action on the input field in the header.
 * @param {string} label - The label of the header to search for.
 * @param {string} inputValue - The value to type into the input field.
 */
Cypress.Commands.add("typeInHeaderInput", (label, inputValue) => {
    cy.findByRole("table")
        .find("th")
        .should("exist")
        .filter(`:contains(${label})`)
        .invoke("index")
        .then((columnIndex) => {
            cy.findByRole("table")
                .find("thead")
                .find("tr")
                .first()
                .find("th")
                .eq(columnIndex)
                .find("input")
                .focus()
                .type(inputValue);
        });
});

/**
 * Custom Cypress command to check the existence of a specific text within a given selector after waiting for a specified amount of time for the DOM to load.
 *
 * @param {string} selector - The selector to search within.
 * @param {number} waitSecs - The time to wait in seconds for the DOM to load before checking for text existence (default is 3 seconds).
 */
Cypress.Commands.add("checkElementIsVisible", (selector, waitSecs = 3) => {
    const checkExistence = () => {
        cy.get("body").then(($body) => $body.find(selector).length > 0);
    };
    return cy.wait(waitSecs * 1000).then(() => checkExistence());
});

/**
 * Custom Cypress command to click on the navigation links by passing just the name of the link or optional
 * sub link for dropdown
 */

Cypress.Commands.add("navigateTopNav", (linkName, itemToClick = null) => {
    const navigationPath = itemToClick ? `${linkName} > ${itemToClick}` : linkName;
    const failedPages = [];

    // Centralized toast handling
    const handleToastNotification = () => {
        const toastSelector = ".Toastify__toast";
        const toastBodySelector = ".Toastify__toast-body";
        const closeBtnSelector = '[aria-label="close"]';
        const errorMessage =
            "DataBee cannot process any data without a configured data lake. Users who are not administrators are unable to log in to the application.";

        return cy.get("body").then(($body) => {
            if ($body.find(toastSelector).length > 0) {
                // Wait for the toast to be visible and interactable
                return cy
                    .get(toastSelector, { timeout: 10000 })
                    .should("be.visible")
                    .within(() => {
                        cy.get(toastBodySelector)
                            .invoke("text")
                            .then((text) => {
                                cy.log(`Toast message: ${text}`);
                                if (text.includes(errorMessage)) {
                                    // Click the close button and confirm toast disappears
                                    cy.get(closeBtnSelector).click({ force: true });
                                }
                            });
                    })
                    .then(() => {
                        // Wait for toast to disappear
                        cy.get(toastSelector).should("not.exist");
                    });
            }
            cy.log("No toast notification found");
            return cy.wrap(null); // ensure chaining continues
        });
    };
    // Perform navigation and error tracking
    const performNavigation = () =>
        // Pre-navigation error check
        cy
            .get("#main-nav")
            .findByRole("link", { name: linkName })
            .as("navLink")
            .then(($el) => {
                // Check if it's a dropdown
                const isDropdown = $el.hasClass("dropdown-toggle");

                // Handle dropdown or direct navigation
                if (isDropdown) {
                    // Click the dropdown toggle with {force: true}
                    cy.get("@navLink").click({ force: true });

                    if (itemToClick) {
                        // Wait explicitly for dropdown to be visible
                        cy.get(".dropdown-menu.show").should("be.visible");
                        // Then find and click the menu item
                        return cy
                            .get(".dropdown-menu.show")
                            .findByRole("menuitem", { name: itemToClick })
                            .should("be.visible")
                            .click();
                    }
                    return cy.get("@navLink");
                }
                // For direct links, use force: true to help with flaky elements
                return cy.get("@navLink").click({ force: true });
            })
            .then(() =>
                // Error check after navigation click
                cy.checkForErrors(`Navigation Click: ${navigationPath}`, failedPages)
            )
            .then(() =>
                // Wait for loading to complete
                cy
                    .get("body")
                    .should("not.contain", "fas fa-spinner")
                    .then(() => cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Loading"))
            )
            .then(() =>
                // Error check after loading
                cy.checkForErrors(`Post-Navigation Loading: ${navigationPath}`, failedPages)
            );
    // Validate and handle any accumulated errors
    const validateNavigation = () =>
        cy.url().then((url) => {
            // Log navigation details
            cy.log(`Navigated to: ${navigationPath}`);
            cy.log(`Current URL: ${url}`);

            // Final error check
            return cy.checkForErrors(`Final Navigation Check: ${navigationPath}`, failedPages).then(() => {
                // Check for and handle any accumulated errors
                if (failedPages.length > 0) {
                    const errorSummary = failedPages
                        .map(
                            (fail) => `Path: ${navigationPath}\nURL: ${fail.url}\nError: ${fail.error || fail.message}`
                        )
                        .join("\n");

                    throw new Error(`Navigation errors occurred:\n${errorSummary}`);
                }

                // Return navigation details
                return {
                    url,
                    linkName,
                    itemToClick,
                };
            });
        });

    // Execute navigation steps
    return handleToastNotification()
        .then(() => performNavigation())
        .then(() => validateNavigation());
});

Cypress.Commands.add("validateToastMessage", (message) => {
    cy.get(".Toastify__toast-body").children().eq(1).findByText(message).should("be.visible");
    cy.findByLabelText("close").click();
});

/**
 * Custom Cypress command to click on the navigation links by passing just the name of the link or optional
 * sub link for dropdown.
 *
 * @param {string} main - The main navigation link title to click.
 * @param {string} [tab] - Optional. The sub link or tab text to click within the dropdown or context.
 * @param {string} [item] - Optional. The sub link or tab text  or item text to click within the dropdown or context.
 *
 *
 * @example
 * cy.navigateTopRightNav("Main Title"); // Clicks on the main navigation link "Main Title"
 * cy.navigateTopRightNav("Main Title", "Sub Link"); // Clicks on "Sub Link" within the dropdown of "Main Title"
 *  * cy.navigateTopRightNav("Main Title", "Sub Link", "Item Text"); // Clicks on "Item Text" within the dropdown of "Main Title and sub link"
 */

Cypress.Commands.add("navigateTopRightNav", (main, sub = null, item = null) => {
    let navigationPath = main;

    if (sub) {
        navigationPath += ` > ${sub}`;
    }

    if (item) {
        navigationPath += ` > ${item}`;
    }

    navigationPath = navigationPath.replace(/\s+>\s+/g, " > ").trim();
    const failedPages = [];

    // Capture failed URL in error tracking
    const captureFailedUrl = () => {
        const currentUrl = cy.state("window").location.href;
        return currentUrl;
    };

    // Centralized toast handling
    const handleToastNotification = () => {
        const toastSelector = ".Toastify__toast";
        const toastBodySelector = ".Toastify__toast-body";
        const closeBtnSelector = '[aria-label="close"]';
        const errorMessage =
            "DataBee cannot process any data without a configured data lake. Users who are not administrators are unable to log in to the application.";

        return cy.get("body").then(($body) => {
            if ($body.find(toastSelector).length > 0) {
                // Wait for the toast to be visible and interactable
                return cy
                    .get(toastSelector, { timeout: 10000 })
                    .should("be.visible")
                    .within(() => {
                        cy.get(toastBodySelector)
                            .invoke("text")
                            .then((text) => {
                                cy.log(`Toast message: ${text}`);
                                if (text.includes(errorMessage)) {
                                    // Click the close button and confirm toast disappears
                                    cy.get(closeBtnSelector).click({ force: true });
                                }
                            });
                    })
                    .then(() => {
                        // Wait for toast to disappear
                        cy.get(toastSelector).should("not.exist");
                    });
            }
            cy.log("No toast notification found");
            return cy.wrap(null); // ensure chaining continues
        });
    };

    // Rest of the code remains the same as in the original implementation
    // Check current navigation state
    const verifyCurrentNavigation = () =>
        cy.get("body").then(($body) => {
            const isMainActive = $body.find(`[title="${main}"].active`).length > 0;
            const isSubActive = sub
                ? $body.find(`[role="menuitem"][aria-selected="true"]:contains("${sub}")`).length > 0
                : true;

            return isMainActive && isSubActive;
        });

    // Navigate to main section
    const navigateToMain = () => {
        cy.findByTitle(main)
            .parent()
            .click()
            .then(() => {
                const failedUrl = captureFailedUrl();
                cy.checkForErrors(`Main Navigation Click: ${main}`, failedPages, failedUrl);
            });
    };

    // Navigate to sub-section
    const navigateToSub = () => {
        if (sub) {
            cy.findByRole("menuitem", { name: sub })
                .click()
                .then(() => {
                    const failedUrl = captureFailedUrl();
                    cy.checkForErrors(`Sub Navigation Click: ${sub}`, failedPages, failedUrl);
                });
        }
    };

    // Navigate to specific item
    const navigateToItem = () => {
        if (item) {
            cy.findByText(item)
                .should("be.visible")
                .click()
                .then(() => {
                    const failedUrl = captureFailedUrl();
                    cy.checkForErrors(`Item Navigation Click: ${item}`, failedPages, failedUrl);
                });
        }
    };

    // Main execution flow
    const executeNavigation = () =>
        verifyCurrentNavigation().then((isCorrectPage) => {
            if (!isCorrectPage) {
                // Navigate to main if not active
                navigateToMain();

                // Navigate to sub if specified
                if (sub) {
                    navigateToSub();
                }
            }

            // Navigate to specific item if specified
            navigateToItem();
            cy.get("body").should("not.contain", "fas fa-spinner");

            cy.retryTextNotContainCheck(".alert.alert-info.fade.show", " Fetching");
            // Final error check with URL capture
            const finalFailedUrl = captureFailedUrl();
            cy.checkForErrors(`Navigation Verification: ${navigationPath}`, failedPages, finalFailedUrl);
        });

    // Error handling
    const handleErrors = () => {
        if (failedPages.length > 0) {
            const errorSummary = failedPages
                .map((page) => `Path: ${navigationPath}\nError: ${page.message}\nURL: ${page.url || "N/A"}`)
                .join("\n");

            throw new Error(`Top Right Navigation Errors:\n${errorSummary}`);
        }

        // Return navigation details
        return {
            main,
            sub,
            item,
        };
    };

    // Execute navigation
    return handleToastNotification()
        .then(() => executeNavigation())
        .then(handleErrors);
});
/**
 * Verifies the value of an input field based on its label.
 *
 * @param {string} label - The label text associated with the input field.
 * @param {string|boolean} expectedValue - The expected value to verify. For checkboxes and radio buttons, use a boolean.
 *
 * @example
 * cy.verifyInputValue("Username", "expectedUsername"); // Verifies the input field with label "Username" has value "expectedUsername"
 * cy.verifyInputValue("Password", "expectedPassword"); // Verifies the input field with label "Password" has value "expectedPassword"
 * cy.verifyInputValue("Remember Me", true); // Verifies the checkbox with label "Remember Me" is checked
 * cy.verifyInputValue("User Type", "Admin"); // Verifies the react-select input with label "User Type" has value "Admin"
 */
Cypress.Commands.add("verifyInputValue", (label, expectedValue) => {
    // Find the element by label and get its ID, type, and class
    cy.findAllByLabelText(label).should("exist").first().invoke("attr", "id").as("getById");
    cy.findAllByLabelText(label).should("exist").first().invoke("attr", "type").as("getByType");
    cy.findAllByLabelText(label).should("exist").first().invoke("attr", "class").as("getByClass");

    // Use the retrieved attributes to find the element and verify its value
    cy.get("@getById").then((selectorId) => {
        cy.get("@getByType").then((inputType) => {
            cy.get("@getByClass").then((className) => {
                const element = cy.get(`[id="${selectorId}"]`);

                if (
                    (className !== "react-select__input" && inputType === "text") ||
                    inputType === "number" ||
                    inputType === "textfield" ||
                    inputType === "password"
                ) {
                    // For text-like inputs, check the value
                    element.should("have.value", expectedValue);
                } else if (inputType === "checkbox" || inputType === "radio") {
                    element.scrollIntoView().then(($el) => {
                        // For checkboxes and radio buttons, check if they are checked/unchecked
                        if (expectedValue) {
                            cy.wrap($el).should("be.checked");
                        } else {
                            cy.wrap($el).should("not.be.checked");
                        }
                    });
                } else if (className.includes("react-select__input")) {
                    cy.findAllByText(expectedValue).should("exist");
                } else {
                    // Fallback for elements without type or class, just check the text
                    element.should("have.value", expectedValue);
                }
            });
        });
    });
});

/**
 * Custom command to fetch the token by logging in with provided baseUrl, username, and password
 * @param {string} baseUrl - The base URL of the API
 * @param {string} username - The username for authentication
 * @param {string} password - The password for authentication
 */
Cypress.Commands.add("getAuthToken", (baseUrl, username, password) =>
    cy
        .request({
            method: "POST",
            url: `${baseUrl}api/login/`,
            body: {
                username,
                password,
            },
        })
        .then((response) => {
            expect(response.status).to.eq(200);
            return response.body.token.access;
        })
);

/**
 * Custom command to make a GET request with the Authorization token
 * @param {string} baseUrl - The base URL of the API
 * @param {string} url - The URL to make the request to
 * @param {string} username - The username for authentication
 * @param {string} password - The password for authentication
 */
let cachedToken = null;
let tokenExpiryTime = null;

Cypress.Commands.add("fetchDataWithToken", (baseUrl, url, username, password) => {
    const now = new Date().getTime();

    // Check if the token is available and hasn't expired
    if (cachedToken && tokenExpiryTime && now < tokenExpiryTime) {
        cy.log("Reusing cached token");
        return cy
            .request({
                method: "GET",
                url,
                headers: {
                    Authorization: `Bearer ${cachedToken}`,
                },
            })
            .then((response) => {
                if (response.status !== 200 || !response.body) {
                    cy.log(`Error fetching data from: ${url}`);
                    return null;
                }
                return response.body;
            });
    }

    // Fetch a new token if it's not cached or expired
    cy.log("Fetching a new token");
    return cy.getAuthToken(baseUrl, username, password).then((token) => {
        cachedToken = token;
        tokenExpiryTime = now + 3600 * 1000; // Set expiry time (1 hour)
        return cy
            .request({
                method: "GET",
                url,
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            })
            .then((response) => {
                if (response.status !== 200) {
                    cy.log(`Error fetching data from: ${url} - Status: ${response.status}`);
                }
                return response.body;
            });
    });
});

/**
 * Custom command to verify the content of a table and perform actions based on the presence of specific text.
  @param {Object} options - Configuration object for table verification
 * @param {string} options.tableSelector - CSS selector for the table element
 * @param {string} options.noDataText - Text indicating no data is present in the table
 * @param {Object} [options.actions] - Optional object with callback functions and additional configuration
 * @param {Function} [actions.onTextFound] - The action to perform if the specified text is found in the table.
 * @param {Function} [actions.onTextNotFound] - The fallback action to perform if the specified text is not found.
 *
 */
Cypress.Commands.add("verifyTableContentWithCondition", (options) => {
    const { tableSelector, noDataText, actions = {}, modalSelector = "" } = options;

    const tableContext = modalSelector ? cy.get(modalSelector).find(tableSelector) : cy.get(tableSelector);

    tableContext
        .find("tbody")
        .invoke("text")
        .then((txt) => {
            if (txt.includes(noDataText)) {
                // Action when the text is found
                actions.onTextFound();
            } else {
                // Action when the text is not found
                actions.onTextNotFound();
            }
        });
});

/**
 * Custom Cypress command to delete a specific value from a multi-select field.
 * If the value is not present in the field, the function will gracefully handle it
 * without failing the test.
 *
 * @param {string} value - The value to be removed from the multi-select field.
 *
 * @example
 * // Example usage
 * cy.deleteValueFromMultiSelectField('Option 1');
 */
Cypress.Commands.add("deleteValueFromMultiSelectField", (value) => {
    // Locate all elements containing the specified value
    cy.findAllByText(value)
        .parents(".react-select__multi-value")
        .then(($elements) => {
            if ($elements.length > 0) {
                // If the value exists, iterate over all matched elements and remove them
                cy.wrap($elements).each(($element) => {
                    cy.wrap($element).find('[role="button"][aria-label^="Remove"]').click();
                });
            } else {
                // If the value does not exist, log a message indicating it was not found
                cy.log(`Value "${value}" not found in the multi-select field.`);
            }
        });
});

/**
 * Custom command to get user information from the API
 * First gets authentication token, then uses it to fetch user details
 * Stores the user info in Cypress environment variable for later use
 *
 * Usage example:
 * cy.getUserInfo();
 * cy.wrap(Cypress.env('userInfo')).then(userInfo => {
 *   // Access userInfo.first_name, userInfo.last_name etc.
 * });
 *
 * @example
 * cy.getUserInfo();
 * cy.contains(`${Cypress.env('userInfo').first_name} ${Cypress.env('userInfo').last_name}`);
 */
Cypress.Commands.add("getUserInfo", () => {
    cy.getAuthToken(
        Cypress.env("credentials").DATABEE_UI,
        Cypress.env("credentials").DATABEE_USER_IDS.ADMIN,
        Cypress.env("credentials").DATABEE_ADMIN_PASSWORD
    ).as("authToken");

    cy.get("@authToken").then((token) => {
        cy.request({
            method: "GET",
            url: `${Cypress.env("credentials").DATABEE_UI}api/authenticate/user-info/`,
            headers: {
                Authorization: `Bearer ${token}`,
            },
        }).then((response) => {
            Cypress.env("userInfo", response.body);
        });
    });
});

Cypress.Commands.add("selectOption", (selector, option, waitSecs = 1) => {
    cy.get(selector)
        .click({ force: true })
        .wait(waitSecs * 1000)
        .type(`${option}{enter}`, { force: true });
});

Cypress.Commands.add("checkForErrors", (pageName, failedPages) => {
    // Check for red alert banners or error messages
    cy.get("body").then(($body) => {
        const alerts = $body.find(".alert-banner.error, .error-message, .alert.alert-danger.show");

        if (alerts.length > 0) {
            let errorsCount = 0;
            let functionalAlertsCount = 0;

            // Convert jQuery collection to array to use forEach
            const alertArray = alerts.toArray();
            alertArray.forEach(($alertElement) => {
                const $alert = Cypress.$($alertElement);
                const alertText = $alert.text().trim();

                // Ignore session expiration messages - these are functional alerts, not errors
                if (
                    alertText.includes("Your session will expire") ||
                    alertText.includes("Click anywhere to renew your session")
                ) {
                    functionalAlertsCount += 1;
                    cy.log(`Functional alert found on ${pageName} page: ${alertText}`);
                } else {
                    // This is a genuine error
                    errorsCount += 1;
                    cy.log(`Error on ${pageName} page: ${alertText}`);
                    // Add the page name and error message to the failed pages list
                    failedPages.push({
                        page: pageName,
                        message: alertText,
                        error: alertText,
                        url: cy.state("url"),
                    });
                }
            });

            // Log summary based on what was found
            if (errorsCount === 0) {
                cy.log(
                    `No errors found on ${pageName} page${
                        functionalAlertsCount > 0 ? " (functional alerts only)" : ""
                    }.`
                );
            }
        } else {
            cy.log(`No alerts or errors found on ${pageName} page.`);
        }
    });
});

Cypress.Commands.add("getCronAfterMinutes", (minutes) => {
    const now = new Date();
    const future = new Date(now.getTime() + minutes * 60 * 1000);

    const utcMinute = future.getUTCMinutes();
    const utcHour = future.getUTCHours();

    return `${utcMinute} ${utcHour} * * *`;
});
