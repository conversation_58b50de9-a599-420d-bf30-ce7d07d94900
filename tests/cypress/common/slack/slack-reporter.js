const { IncomingWebhook } = require("@slack/webhook");
const ReportMessage = require("./slack-message-builder");
const slackData = require("./slackData.json");

class SlackReporter {
    // eslint-disable-next-line class-methods-use-this -- This method intentionally does not use this
    sendMessage(body, webhookUrl) {
        const webhook = new IncomingWebhook(webhookUrl);
        webhook.send(body);
    }

    sendPostMessage() {
        const message = new ReportMessage();
        const messageBody = message.build(slackData);
        const webhookUrl = process.env.SLACK_WEBHOOK_URL || "";

        this.sendMessage(messageBody, webhookUrl);
    }
}
module.exports = SlackReporter;
