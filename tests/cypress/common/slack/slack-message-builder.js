const successColor = "#2EB67D";
const failedColor = "#E01E5A";
const successEmoji = ":pht_pass:";
const failEmoji = ":pht_fail:";
const gitHubRunNumber = process.env.GITHUB_RUN_NUMBER;
const gitHubRunId = process.env.GITHUB_RUN_ID;
const gitHubUrl = process.env.GITHUB_URL;
const gitHubBuild = `${gitHubUrl}/actions/runs/${gitHubRunId}`;
const allureReportPage = `https://d1tuiz30h3f5r2.cloudfront.net/Allure-Report-${gitHubRunNumber}/index.html`;

class SlackMessageBuilder {
    build(results) {
        const failed = results.totalFailed > 0;
        const result = failed ? "Failed" : "Passed";
        const color = failed ? failedColor : successColor;
        const resultsEmoji = failed ? failEmoji : successEmoji;

        let buildInfoText;
        let buildLink;
        let allureLink;

        if (gitHubRunId !== undefined) {
            buildInfoText = `*Browser:* ${results.browserName.toUpperCase()}
            \n *Browser Version:* ${results.browserVersion}
            \n *Environment:* ${results.config.env.ENVIRONMENT.toUpperCase()}`;
            buildLink = `*Build:* <${gitHubBuild}|#${gitHubRunNumber}> :github:`;
            allureLink = `*Allure Report:* <${allureReportPage}|View> :bar_chart:`;
        } else {
            buildInfoText = `*Browser:* ${results.browserName.toUpperCase()}
            \n *Browser Version:* ${results.browserVersion}
            \n *Environment:* ${results.config.env.ENVIRONMENT.toUpperCase()}`;
            buildLink = "*No Build Link - Test Ran Locally* :github:";
            allureLink = "*Report Generated* :bar_chart:";
        }

        this.messageBody = {
            attachments: [
                {
                    color,
                    blocks: [
                        {
                            type: "header",
                            text: {
                                type: "plain_text",
                                text: `Test Suite: ${results.config.projectName} :bee:`,
                                emoji: true,
                            },
                        },
                        {
                            type: "divider",
                        },
                        {
                            type: "section",
                            fields: [
                                {
                                    type: "mrkdwn",
                                    text: `*Build Results:* ${result} ${resultsEmoji}
                                    \n *Number of Tests:* ${results.totalTests}
                                    \n *Failed:* ${results.totalFailed}
                                    \n *Passed:* ${results.totalPassed}
                                    \n *Skipped:* ${results.totalSkipped}`,
                                },
                                {
                                    type: "mrkdwn",
                                    text: buildInfoText,
                                },
                            ],
                        },
                        {
                            type: "divider",
                        },
                        {
                            type: "section",
                            fields: [
                                {
                                    type: "mrkdwn",
                                    text: buildLink,
                                },
                                {
                                    type: "mrkdwn",
                                    text: allureLink,
                                },
                            ],
                        },
                    ],
                },
            ],
        };

        return this.messageBody;
    }
}

module.exports = SlackMessageBuilder;
