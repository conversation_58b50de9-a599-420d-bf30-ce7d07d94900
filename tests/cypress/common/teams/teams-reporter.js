const { IncomingWebhook } = require("@slack/webhook");
const ReportMessage = require("./teams-message-builder");
const teamsData = require("./teamsData.json");

class TeamsReporter {
    // eslint-disable-next-line class-methods-use-this -- This method intentionally does not use this
    sendMessage(body, webhookUrl) {
        const webhook = new IncomingWebhook(webhookUrl);
        webhook.send(body);
    }

    sendPostMessage() {
        const message = new ReportMessage();
        const messageBody = message.build(teamsData);
        const webhookUrl = process.env.TEAMS_WEBHOOK_URL || "";
        const webhookUrlDemo = process.env.TEAMS_WEBHOOK_URL_DEMO || "";

        if (process.env.DEMO_ALERTS && teamsData.totalFailed > 0) {
            this.sendMessage(messageBody, webhookUrlDemo);
        }
        this.sendMessage(messageBody, webhookUrl);
    }
}
module.exports = TeamsReporter;
