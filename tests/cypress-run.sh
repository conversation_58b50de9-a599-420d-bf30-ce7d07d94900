#!/bin/zsh

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'


aws_secrets=("stg-demo-staging-eng" "demo-staging" "sales2-demo" "stg-latest-snowflake" "stg-test-snowflake" "stg-test-datafaker" "local" "stgtest-e2eauto")


run_cypress() {
  echo "Do you want to run a specific spec file? (yes/no)"
  read -r run_spec

  if [[ -z "$run_spec" ]]; then
    echo "${RED}No value was specified!, Try Again!${NC}"
    exit 1
  fi

  if [[ "$run_spec" != "yes" ]] && [[ "$run_spec" != "no" ]]; then
    echo "${RED}Invalid value was specified!, Try Again!${NC}"
    exit 1
  fi

  if [[ "$run_spec" == "yes" ]]; then
    echo "Provide the spec file path:"
    read -r spec_path
  fi

  echo "Do you want cypress to run headed or headless? (headed/headless)"
    read -r run_mode

  if [[ -z "$run_mode" ]]; then
    echo "${RED}No value was specified!, Try Again!${NC}"
    exit 1
  fi

  if [[ "$run_mode" != "headed" ]] && [[ "$run_mode" != "headless" ]]; then
    echo "${RED}Invalid value was specified!, Try Again!${NC}"
    exit 1
  fi

  echo "Do you want to see a report after the tests? (yes/no)"
  read -r show_report

  if [[ -z "$show_report" ]]; then
    echo "${RED}No value was specified!, Try Again!${NC}"
    exit 1
  fi

  if [[ "$show_report" != "yes" ]] && [[ "$run_spec" != "no" ]]; then
    echo "${RED}Invalid value was specified!, Try Again!${NC}"
    exit 1
  fi

  if [[ "$show_report" == "yes" ]]; then
      echo "${YELLOW}Cleaning old report...${NC}"
      yarn clean:report
  fi

  if [[ -n "$spec_path" ]]; then
    trimmed_spec_path="${spec_path#tests/}"
    echo "${GREEN}Running Cypress tests in ${YELLOW}$run_mode${NC} ${GREEN}mode, with spec: ${YELLOW}$(basename "$spec_path")${NC}${GREEN}, using secret: ${YELLOW}$aws_secret_name${NC}"
    if [[ -n "$aws_secret_name" ]]; then
        yarn cypress run --spec "$trimmed_spec_path" --env ENVIRONMENT="$aws_secret_name" --browser chrome --"$run_mode" --config-file cypress.config.shared.js
    fi
  else
    echo "${GREEN}Running Cypress tests in ${YELLOW}$run_mode${NC} ${GREEN}mode, using secret: ${YELLOW}$aws_secret_name${NC}"
    yarn cypress run --env ENVIRONMENT="$aws_secret_name" --browser chrome --"$run_mode" --config-file cypress.config.shared.js
  fi

  if [[ "$show_report" == "yes" ]]; then
    echo "${YELLOW}Generating Report...${NC}"
    yarn gen:report
    echo "${YELLOW}Opening Allure report...${NC}"
    ./node_modules/allure-commandline/bin/allure open allure-report
  fi
}

open_cypress() {
  echo "${GREEN}Running Cypress tests in $run_type mode, using secret: $aws_secret_name${NC}"
  yarn cypress open --env ENVIRONMENT="$aws_secret_name" --browser chrome --config-file cypress.config.shared.js
}

echo "Do you want to use local or AWS for test credentials? (local_creds/aws)"
read -r credentials_type

if [[ -z "$credentials_type" ]]; then
  echo "${RED}No value was specified!, Try Again!${NC}"
  exit 1
fi

if [[ "$credentials_type" != "aws" ]] && [[ "$credentials_type" != "local_creds" ]]; then
  echo "${RED}Invalid value was specified!, Try Again!${NC}"
  exit 1
fi

if [[ "$credentials_type" == "aws" ]]; then
  echo "${YELLOW}Are you already authenticated with AWS? (yes/no)${NC}"
  read -r auth_aws


echo "Select AWS secret name:"
PS3="Please select an AWS secret name by typing the corresponding number: "

select aws_secret_name in "${aws_secrets[@]}"; do
  if [ -n "$aws_secret_name" ]; then
    echo "${GREEN}You have selected: ${YELLOW}$aws_secret_name${NC}"
    break
  else
    echo -e "${RED}Invalid selection. Please try again.${NC}"
  fi
done

  if [[ -z "$aws_secret_name" ]]; then
    aws_secret_name="local_creds"
  fi

  if [[ "$auth_aws" == "no" ]]; then
      echo "${YELLOW}Authenticating with AWS...${NC}"
      aadawscli --profile-name default
  fi
else
  aws_secret_name="local_creds"
fi

echo "Do you want to use cypress GUI or CLI? (gui/cli)"
read -r run_type

if [[ -z "$run_type" ]]; then
  echo "${RED}No value was specified!, Try Again!${NC}"
  exit 1
fi

if [[ "$run_type" != "gui" ]] && [[ "$run_type" != "cli" ]]; then
  echo "${RED}Invalid value was specified!, Try Again!${NC}"
  exit 1
fi

if [[ "$run_type" == "gui" ]]; then
  open_cypress
elif [[ "$run_type" == "cli" ]]; then
  run_cypress
fi
