# Local Development/Runs USE ONLY - To use local creds
# set the --env flag and ENVIRONMENT key to local_creds.

# Create a copy of this file into a new file calling it .env by running
# cp template.env .env

# Only update the values that are needed to use for tests running.
# NOTE: if your test requires CRUD to AWS you must authenticate with AWS

# DataBee password for all logins(Cluster, tenant, ...), we keep it same for simplicity sake.
DATABEE_USER_PASS=""
# DataBee Cluster API URL
CLUSTER_API="https://acme-dev-admin.databee.local.com:17000/"
# Cluster UI URL
CLUSTER_UI="https://acme-dev-admin.databee.local.com:3001"
# Cluster Admin User Email for edge cases ONLY
CLUSTER_ADMIN_USER_TEST=""
# Cluster Support User Email for edge cases ONLY
CLUSTER_SUPPORT_USER_TEST=""
# Cluster Admin User Email for most tests
CLUSTER_ADMIN="<EMAIL>"
# Cluster Support User Email for most tests
CLUSTER_SUPPORT="<EMAIL>"
# Dashboard Reset Tenant URL for any cluster that has a dasboard reset tenant
DASHBOARD_RESET_URL=""
# Admin user email for dashboard reset tenant
DASHBOARD_RESET_ADMIN=""
# Analyst user email for dashboard reset tenant
DASHBOARD_RESET_ANALYST=""
# Data Engineer user email for dashboard reset tenant
DASHBOARD_RESET_DATA_ENGINEER=""
# The name of DataBee Tenant that shows on the Cluster tenants page
DATABEE_UI_NAME="acme-dev"
# The Url for DataBee Tenant
DATABEE_UI="https://acme-dev.databee.local.com:3000"
# The DataBee Tenant ID that shows on the Cluster tenants page
DATABEE_UI_ID=1
# DataBee Tenant Admin user email for edge cases
DATABEE_ADMIN_USER_TEST=""
# DataBee Tenant Analyst user email for edge cases
DATABEE_ANALYST_USER_TEST=""
# DataBee Tenant Admin user email
DATABEE_ADMIN="<EMAIL>"
# DataBee Tenant Analyst user email
DATABEE_ANALYST="<EMAIL>"
# DataBee Tenant Data Engineer user email
DATABEE_DATAENGINEER="<EMAIL>"
# This is a username for DataBee Compliance user
DATABEE_COMPLIANCE_ROLE_USERNAME="<EMAIL>"
# The URL for a tenant that is ONLY used to disable in a test
CYPRESS_DISABLE_URL="https://acme-dev-disabled.databee.local.com:3000"
# The Admin user email for the tenant set to be disabled as part of the test case
CYPRESS_DISABLE_ADMIN_USERNAME=""
# The tenant name of the tenant set to be disabled as part of the test case
CYPRESS_DISABLE_UI_NAME="acme-dev-disabled"
# Below 7 keys are for Snowflake connection
SNOWFLAKE_ACCOUNT_IDENTIFIER=""
SNOWFLAKE_USERNAME=""
SNOWFLAKE_PASSWORD=""
SNOWFLAKE_ROLE=""
SNOWFLAKE_WAREHOUSE=""
SNOWFLAKE_DATABASE=""
SNOWFLAKE_PRIVATE_KEY=""
# Below 3 are for test that need API Ingest credentials in the test
API_INGEST_BASIC_USERNAME=""
API_INGEST_BASIC_PASSWORD=""
API_INGEST_BASIC_BASE_URL=""
# Below 4 values are used for content delivery related tests
CONTENT_DELIEVERY_USERNAME=""
CONTENT_DELIEVERY_CLIENT_ID=""
CONTENT_DELIEVERY_CLIENT_SECRET=""
CONTENT_DELIEVERY_SECRET_VALUE=""
# This is the DataBee Tenants API URL
TENANT_API=""
# This is the GitHub URL for GitHub Repositories under Rules Management
GITHUB_URL=""
# ServiceNow Credentials
SERVICENOW_AUTH_TYPE=""
SERVICENOW_USERNAME="""
SERVICENOW_PASSWORD=""
SERVICENOW_CLIENT_KEY=""
SERVICENOW_CLIENT_SECRET=""
SERVICENOW_TOKEN_URL=""