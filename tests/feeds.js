const yaml = require("js-yaml");
const fs = require("fs");

const DATASOURCES_PATH = "cypress/fixtures/datasources.json";
const WINDOWS_EVENT_LOG = "windows_event_log";

// Reading feeds.yml to get list of all ingested data feeds in github workflow
async function readFeedSchema(os) {
    const yamlContent = fs.readFileSync("cypress/context/feeds.yml");
    const { feeds } = yaml.load(yamlContent);
    const filteredFeeds = {};
    Object.keys(feeds).forEach((feedName) => {
        // WINDOWS_EVENT_LOG  is only supported on windows os
        if (!(feedName.includes(WINDOWS_EVENT_LOG) && os !== "Windows")) {
            filteredFeeds[feedName] = feeds[feedName];
        }
    });
    return filteredFeeds;
}

// We are storing data feed ids when we create data feed
// Read file to get all data feed ids created during single github workflow
async function readDatasourceIds() {
    let datasourceIds = {};
    try {
        const dsContent = fs.readFileSync(DATASOURCES_PATH, { encoding: "utf8" });
        datasourceIds = JSON.parse(dsContent);
    } catch (e) {
        throw new Error("error reading datasources");
    }
    return datasourceIds;
}

// Sample output files are stored in feeds folder
// Read sample output file for feed
async function readFeedExpectedOutputs(feedName) {
    const feedOutputPath = `cypress/fixtures/feeds/${feedName}/tests/data/output`;
    const files = fs.readdirSync(feedOutputPath).filter((file) => file.endsWith(".json"));
    const outputs = [];
    files.forEach((file) => {
        const fileContent = fs.readFileSync(`${feedOutputPath}/${file}`, { encoding: "utf8" });
        try {
            outputs.push(JSON.parse(fileContent));
        } catch (e) {
            throw new Error(`error reading file: ${file}`);
        }
    });
    return outputs;
}

// Combine data feed and output file of feed into single object
async function loadFeeds(collectorName, os) {
    const feedOutputsMap = {};
    if (fs.existsSync(DATASOURCES_PATH)) {
        const feeds = await readFeedSchema(os);
        const datasourceIds = await readDatasourceIds();
        Object.keys(feeds).forEach(async (feedName) => {
            // We don't have sample output files for WINDOWS_EVENT_LOG
            let outputs = [];
            if (feedName !== WINDOWS_EVENT_LOG) {
                outputs = await readFeedExpectedOutputs(feedName);
            }
            feedOutputsMap[feedName] = {
                outputs,
                // Get data feed id by name of data feed
                // data feed name is combination of data collector and yml data feed name
                datasources: feeds[feedName].map((ds) => {
                    const dsIdKey = `${ds.name}_${collectorName}`;
                    const datasourceId = datasourceIds[dsIdKey];
                    if (datasourceId) {
                        return { ...ds, datasourceId };
                    }
                    return ds;
                }),
            };
        });
    }
    return feedOutputsMap;
}

module.exports = {
    loadFeeds,
};
