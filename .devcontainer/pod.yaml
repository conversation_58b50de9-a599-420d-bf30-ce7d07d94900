apiVersion: v1
kind: Pod
metadata:
  name: pod-template
spec:
  affinity:
    podAffinity:
      RequiredDuringSchedulingIgnoredDuringExecution: []
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: NodeType
                operator: NotIn
                values:
                  - primary
  tolerations:
    - key: "NodeType"
      operator: "Equal"
      value: "devpod"
      effect: "NoSchedule"
  containers:
    - name: devpod
      resources:
        requests:
          cpu: 2
      volumeMounts:
        - mountPath: /workspaces/.home
          name: devpod
          subPath: devpod/home
      envFrom:
        - configMapRef:
            name: iris-cm
      # env:
      # - name: POSTGRES_PASSWORD
      #   valueFrom:
      #     secretKeyRef:
      #       name: postgresql-credentials
      #       key: postgres-password
      # - name: CONSUL_HTTP_TOKEN
      #   valueFrom:
      #     secretKeyRef:
      #       name: iris-consul-bootstrap-acl-token
      #       key: token
      # - name: POOL_PRE_PING
      #   value: "true"