#!/usr/bin/env bash

mkdir -p $HOME/.config/pip
export PIP_CONF_LOCATION=$HOME/.config/pip/pip.conf

echo "[global]" >> $PIP_CONF_LOCATION
aws secretsmanager get-secret-value --secret-id service-user-artifactory --region us-east-1 --output text --query SecretString | jq '"index-url = https://\(.ARTIFACTORY_USERNAME):\(.ARTIFACTORY_TOKEN)@\(.ARTIFACTORY_HOST)/\(.ARTIFACTORY_PACKAGE)"' -r >> $PIP_CONF_LOCATION

#install cypress
yarn add cypress --dev
cd tests
yarn install
yarn prepare
cd ..

rm $PIP_CONF_LOCATION

#install and configure pytest for the workspace to generate allure report and serve
sudo apt-get install default-jre
wget https://github.com/allure-framework/allure2/releases/download/2.30.0/allure-2.30.0.zip
unzip allure-2.30.0.zip -d /opt/allure
echo 'export PATH=$PATH:/opt/allure/allure-2.30.0/bin' >> ~/.bashrc
source ~/.bashrc
rm allure-2.30.0.zip

echo "postCreate done"