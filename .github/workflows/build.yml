name: Validate Source

on:
  pull_request:
    branches:
      - main

env:
  SERVICE_NAME: cypress-tests
  # TODO: current node version (inherrited from iris-application) should be upgraded
  NODE_VERSION: '16.18.0'

defaults:
  run:
    working-directory: ./tests

jobs:
  validate:
    name: <PERSON><PERSON> <PERSON><PERSON>
    runs-on: databee-runner
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup yarn
        uses: comcast-bluebird/actions/setup-yarn@main

      - name: Install Dependencies
        env:
          YARN_NPM_ALWAYS_AUTH: true
          YARN_NPM_AUTH_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }}
        run: yarn install

      - name: Run Linter
        run: yarn run lint

      - name: Uninstall Yarn
        if: always()
        run: npm uninstall -g yarn

      - uses: colpal/actions-clean@v1
        if: always()
