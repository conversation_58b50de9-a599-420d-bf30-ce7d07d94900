name: Feature-branch - Execute Cypress-tests

permissions:
  contents: write
  actions: write

on:
  workflow_dispatch:
    inputs:
      vcluster-name:
        description: VCluster Name
        required: true
        type: string
      spec-file:
        description: Spec File (cypress/e2e/ui/**/**/*.js)
        type: string
      grep-tag:
        description: Select Tag
        type: choice
        default: ''
        options:
          - ''
          - smoke
          - full
          - cluster_test
          - regression
          - realCluster
          - config_user
          - config_sso
          - dashboard
          - my_profile
          - notification
          - entity_view
          - compliance
          - entitlements
          - exposure
          - config
          - data
          - search
          - content_delivery
          - quality_alerts
          - remediation_actions

env:
  NODE_VERSION: '20.16.0'
  ACCOUNT: "databee-us-dev"
  REGION: "us-east-1"
  TEST_TYPE: "SnowFlake"

defaults:
  run:
    working-directory: ./tests

jobs:
  spec-cypress-tests:
    name: Run Cypress Spec Tests
    runs-on: databee-runner
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: get role
        run: aws sts get-caller-identity
  
      - name: Setup AWS
        id: creds
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ env.ACCOUNT }}
          region: ${{ env.REGION }}
          use-runner-role: true

      - name: Setup yarn
        uses: comcast-bluebird/actions/setup-yarn@main

      - name: Set Environment Variables
        env:
          AWS_ACCOUNT: ${{ env.ACCOUNT }}
        run: |
          json_path=$GITHUB_WORKSPACE/.github/settings/accounts.json
          jq -e ".[\"$AWS_ACCOUNT\"]" "$json_path" || {
              echo "Unsupported account: $AWS_ACCOUNT"
              exit 1
          }
          cycloudfront=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.cloudfront" "$json_path")
          echo "CLOUDFRONT_URL=$cycloudfront" >> $GITHUB_ENV

          cybucket=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.s3bucket" "$json_path")
          echo "CYPRESS_REPORT_BUCKET=$cybucket" >> $GITHUB_ENV

      - name: Install Dependencies
        env:
          YARN_NPM_ALWAYS_AUTH: true
          YARN_NPM_AUTH_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }}
        run: yarn install

      - name: Install Packages
        run: |
          apt-get update
          apt-get install xvfb -y
          apt-get install libgbm1 -y
          apt-get install libasound2 -y
          apt-get install default-jdk -y

      - name: Set up Chrome
        run: |
          wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -
          echo "deb [arch=amd64] https://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
          apt-get update
          apt-get install -y google-chrome-stable

      - name: Run Cypress Tests
        env:
          CYPRESS_ENVIRONMENT: ${{ inputs.vcluster-name }}
          CYPRESS_AWS_REGION: ${{ env.REGION }}
          CYPRESS_grepTags: ${{ inputs.grep-tag || '-e2e_setup+-e2e_tests+-e2e_cleanup+-data_faker+-demo_staging' }}
          AWS_REGION: ${{ env.REGION }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds.outputs.aws-session-token }}
        run: |
          export TEST_IN_WORKFLOW=true
          CMD="npx cypress run --browser chrome"

          if [ -n "${{ inputs.spec-file }}" ]; then
          CMD="$CMD --spec '${{ inputs.spec-file }}'"
          fi

          if [ -n "${{ inputs.grep-tag }}" ]; then
          CMD="$CMD --env grepTags=${{ inputs.grep-tag }}"
          fi

          echo "Running: $CMD"
          eval $CMD

      - name: Generate Report
        if: always()
        run: |
          yarn gen:report

      - name: Setup AWS (Refresh connection)
        if: always()
        id: creds-refresh
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: ${{ env.ACCOUNT }}
          region: ${{ env.REGION }}
          use-runner-role: true

      - name: Upload Allure Report to S3
        if: always()
        env:
          AWS_REGION: ${{ env.REGION }}
          AWS_ACCESS_KEY_ID: ${{ steps.creds-refresh.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds-refresh.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds-refresh.outputs.aws-session-token }}
        run: aws s3 cp allure-report s3://${{ env.CYPRESS_REPORT_BUCKET }}/Feature-Test-Allure-Report-${{ github.run_number}} --recursive

      - name: Add Report Link to Summary
        if: always()
        run: |
          {
            echo "### Allure Report"
            echo "[View Report](${{ env.CLOUDFRONT_URL }}/Feature-Test-Allure-Report-${{ github.run_number}}/index.html)"
          } >> $GITHUB_STEP_SUMMARY

      - name: Uninstall Yarn
        if: always()
        run: npm uninstall -g yarn

      - uses: colpal/actions-clean@v1
        if: always()