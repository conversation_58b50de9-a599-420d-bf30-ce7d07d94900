# Cypress Test Workflows for Feature Testing

## 📘 Overview

This guide outlines a streamlined process for running Cypress tests on feature branches using GitHub workflows. It includes:

1. Bootstrap the system for Cypress tests

2. Apply feature branch changes to vCluster

3. Trigger Cypress tests and review Allure reports

4. Destroy vCluster and clean up associated resources

## Step 1: Bootstrap System for Cypress Tests

This automated workflow sets up the environment required for Cypress tests.

### Workflow Details:

- **URL**: Access the [Bootstrap System for Cypress Tests workflow](https://github.com/comcast-bluebird/cypress-tests/actions/workflows/bootstrap-system-for-cypress-tests.yml)

#### 🛠️ Workflow Input Parameters:

| Parameter           | Description                                                                                   | Example Value                    |
|---------------------|-----------------------------------------------------------------------------------------------|----------------------------------|
| **`vcluster-name`**  | (required) Name for the virtual cluster. Should be unique to avoid conflicts.                 | `feat‑AB123‑orders‑UI`          |
| **`profile`**        | Profile for installing DataBee components. Options: `stable`, `latest`, `light`, `test`, `demo`, `demo-live`. | `latest`                         |
| **`snowflake-database`** | Snowflake database name for test configuration.                 | `TEST_DB`                        |


### Workflow Summary:

Upon completion, the workflow provides the `Admin UI URL`, `admin Username`, `AWS account` information, and the name of the `secret` created in the databee-us-dev account.

## Step 2: Apply Feature Branch Code (Manual)

Once the vCluster is provisioned, you must manually apply your feature branch code.

### 📝 Instructions:

1. Navigate to the respective repository containing the feature branch

2. Trigger the `Development Preview` workflow

3. Input your feature branch name and the target vCluster

4. The workflow will apply changes to the provisioned vCluster

## Step 3: Trigger Cypress Tests

Now that the feature code is applied, you can run Cypress tests on the vCluster.

### 🧾 Steps:

1. Go to the cypress-tests repository

2. Trigger the [Execute Cypress Feature Tests workflow](https://github.com/comcast-bluebird/cypress-tests/actions/workflows/execute-cypress-feature-tests.yml)

3. Provide the required inputs as detailed below

### 🛠️ Workflow Input Parameters

| Parameter        | Description |
|------------------|-------------|
| **`vcluster-name`** | Name of the virtual cluster created during the bootstrap step. |
| **`spec-file`**      | Path to specific test files or directories to run (e.g., `cypress/e2e/ui/**/*.js`). |
| **`grep-tag`**       | Tag used to filter tests (e.g., `smoke`, `full`, `dashboard`, etc.). |

---

### 🔍 Test Filtering Logic

The workflow determines which tests to run based on the following logic:

1. **No Filters Provided**  
   If **neither** `spec-file` **nor** `grep-tag` is provided:  
   → **Run all available tests**.

2. **Single Filter Provided**
   - If **only** `spec-file` is provided:  
     → **Run all tests in the specified file(s) or directory**.
   - If **only** `grep-tag` is provided:  
     → **Run all tests matching the specified tag**, regardless of file location.

3. **Both Filters Provided**  
   If **both** `spec-file` and `grep-tag` are provided:  
   → **Run only the tests within the specified file(s)/directory that also match the tag**.

## Step 4: Destroy vCluster and Clean Up

After testing is complete, tear down the environment using the dedicated workflow.

### Workflow Details:

- **URL**: Access the [Destroy System After Cypress Tests workflow](https://github.com/comcast-bluebird/cypress-tests/actions/workflows/destroy-system-after-cypress-tests.yml/badge.svg?branch=main)

### 🛠️ Workflow Input Parameters

| Parameter        | Description |
|------------------|-------------|
| **`vcluster-name`** | Name of the virtual cluster created during the bootstrap step. |

## Best Practices

1. **Resource Management**: Always run the destroy workflow after testing to clean up resources

2. **Naming Convention**: Use descriptive and unique vcluster names (e.g., `cypress-mmaru231`)

3. **Test Selection**: Use specific spec files and tags to run only the tests you need

4. **Report Review**: Check the Allure reports for detailed test results and debugging information

5. **AWS Account**: Always use the `databee-us-dev` AWS account as secrets rely on this account
