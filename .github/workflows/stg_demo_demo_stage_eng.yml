name: Stg Demo Cluster - demo staging eng

permissions:
  contents: write
  actions: write

on:
  workflow_dispatch:
    inputs:
      teams-alert:
        description: Teams Alert
        type: boolean
        default: true
  schedule:
    # Daily pipeline run at 1:00 AM EST & 6:00 AM UTC
    - cron: "0 6 * * *"

env:
  NODE_VERSION: '20.16.0'
  TEAMS_WEBHOOK_URL: ${{ secrets.TEAMS_WEBHOOK_DRY_RUN_URL }}

defaults:
  run:
    working-directory: ./tests

jobs:
  demo-tests:
    name: Run Demo Staging Tests
    runs-on: databee-runner
    env:
      REPORT_NAME: 'Stg-Demo-Cluster-'
      CYPRESS_ENVIRONMENT: 'stg-demo-staging-eng'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: get role
        run: aws sts get-caller-identity
  
      - name: Setup AWS
        id: creds
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: 'databee-us-stg-custom'
          region: 'us-east-1'
          use-runner-role: true

      - name: Setup yarn
        uses: comcast-bluebird/actions/setup-yarn@main

      - name: Set Environment Variables
        env:
          AWS_ACCOUNT: 'databee-us-stg-custom'
        run: |
          json_path=$GITHUB_WORKSPACE/.github/settings/accounts.json
          jq -e ".[\"$AWS_ACCOUNT\"]" "$json_path" || {
              echo "Unsupported account: $AWS_ACCOUNT"
              exit 1
          }
          cycloudfront=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.cloudfront" "$json_path")
          echo "CLOUDFRONT_URL=$cycloudfront" >> $GITHUB_ENV

          cybucket=$(jq -r ".[\"$AWS_ACCOUNT\"].cypress.s3bucket" "$json_path")
          echo "CYPRESS_REPORT_BUCKET=$cybucket" >> $GITHUB_ENV

      - name: Install Dependencies
        env:
          YARN_NPM_ALWAYS_AUTH: true
          YARN_NPM_AUTH_TOKEN: ${{ secrets.SVC_DATABEE_JFROG_TOKEN }}
        run: yarn install

      - name: Install Packages
        run: |
          apt-get update
          apt-get install xvfb -y
          apt-get install libgbm1 -y
          apt-get install libasound2 -y
          apt-get install default-jdk -y

      - name: Set up Chrome
        run: |
          wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -
          echo "deb [arch=amd64] https://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
          apt-get update
          apt-get install -y google-chrome-stable

      - name: Run Cypress Tests
        env:
          AWS_ACCESS_KEY_ID: ${{ steps.creds.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds.outputs.aws-session-token }}
          CYPRESS_ENVIRONMENT: ${{ env.CYPRESS_ENVIRONMENT }}
          CYPRESS_AWS_REGION: 'us-east-1'
          CYPRESS_grepTags: 'demo_staging,demo_tests'
        run: |
          export TEST_IN_WORKFLOW=true
          yarn cypress:test

      - name: Setup AWS (Refresh connection)
        id: creds-refresh
        uses: comcast-bluebird/actions/setup-aws@main
        with:
          account: 'databee-us-stg-custom'
          region: 'us-east-1'
          use-runner-role: true

      - name: Upload Allure Report to S3
        env:
          AWS_REGION: 'us-east-1'
          AWS_ACCESS_KEY_ID: ${{ steps.creds-refresh.outputs.aws-access-key-id }}
          AWS_SECRET_ACCESS_KEY: ${{ steps.creds-refresh.outputs.aws-secret-access-key }}
          AWS_SESSION_TOKEN: ${{ steps.creds-refresh.outputs.aws-session-token }}
        run: |
          aws s3 cp allure-report s3://${{ env.CYPRESS_REPORT_BUCKET }}/Cypress-Test-Results/${{ env.CYPRESS_ENVIRONMENT }}/${{ env.REPORT_NAME }}Allure-Report-${{ github.run_number }} --recursive

      - name: Set Alert Channel
        if: ${{ inputs.teams-alert == true || github.event_name == 'schedule' }}
        run: |
          echo "TEAMS_WEBHOOK_URL=${{ secrets.TEAMS_WEBHOOK_URL }}" >> $GITHUB_ENV

      - name: Send Teams Notification
        if: ${{ inputs.teams-alert == true || github.event_name == 'schedule' }}
        env:
          GITHUB_URL: ${{ github.event.repository.html_url }}
          GITHUB_RUN_ID: ${{ github.run_id }}
          GITHUB_RUN_NUMBER: ${{ github.run_number}}
          NOTIFICATION_CARD_TITLE: 'Stg Demo Staging Cypress Sanity Tests'
          REPORT_NAME: 'Stg-Demo-Cluster-'
        run: |
          yarn teams:report

      - name: Uninstall Yarn
        if: always()
        run: npm uninstall -g yarn

      - uses: colpal/actions-clean@v1
        if: always()

